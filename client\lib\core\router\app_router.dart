import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../layouts/main_layout.dart';
import '../../screens/dashboard_screen.dart';
import '../../screens/quests_screen.dart';
import '../../screens/notifications_screen.dart';
import '../../screens/profile_screen.dart';
import '../../screens/login_screen.dart';
import '../../screens/register_screen.dart';

/// Application router configuration using GoRouter
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/dashboard',
    routes: [
      // Authentication routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      
      // Main app shell with navigation
      ShellRoute(
        builder: (context, state, child) => MainLayout(child: child),
        routes: [
          // Dashboard
          GoRoute(
            path: '/dashboard',
            name: 'dashboard',
            builder: (context, state) => const DashboardScreen(),
          ),
          
          // Quests
          GoRoute(
            path: '/quests',
            name: 'quests',
            builder: (context, state) => const QuestsScreen(),
          ),
          
          // Notifications
          GoRoute(
            path: '/notifications',
            name: 'notifications',
            builder: (context, state) => const NotificationsScreen(),
          ),
          
          // Profile
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfileScreen(),
          ),
        ],
      ),
    ],
    
    // Error handling
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              state.error?.toString() ?? 'Unknown error',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    ),
    
    // Redirect logic for authentication
    redirect: (context, state) {
      // TODO: Implement authentication check
      // For now, allow all routes
      return null;
    },
  );
  
  /// Navigation helper methods
  static void goToDashboard(BuildContext context) {
    context.go('/dashboard');
  }
  
  static void goToQuests(BuildContext context) {
    context.go('/quests');
  }
  
  static void goToNotifications(BuildContext context) {
    context.go('/notifications');
  }
  
  static void goToProfile(BuildContext context) {
    context.go('/profile');
  }
  
  static void goToLogin(BuildContext context) {
    context.go('/login');
  }
  
  static void goToRegister(BuildContext context) {
    context.go('/register');
  }
  
  /// Get current route name
  static String? getCurrentRouteName(BuildContext context) {
    final routerDelegate = GoRouter.of(context).routerDelegate;
    final routeMatch = routerDelegate.currentConfiguration;
    return routeMatch.last.matchedLocation;
  }
  
  /// Check if current route is active
  static bool isRouteActive(BuildContext context, String routeName) {
    final currentRoute = getCurrentRouteName(context);
    return currentRoute == routeName;
  }
}

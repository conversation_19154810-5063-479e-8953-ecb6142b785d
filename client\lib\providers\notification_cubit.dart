import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';

/// Notification states
abstract class NotificationState {}

class NotificationInitial extends NotificationState {}

class NotificationLoading extends NotificationState {}

class NotificationLoaded extends NotificationState {
  final List<Notification> notifications;
  final int unreadCount;

  NotificationLoaded({
    required this.notifications,
    required this.unreadCount,
  });
}

class NotificationError extends NotificationState {
  final String message;

  NotificationError({required this.message});
}

/// Notification management cubit
class NotificationCubit extends Cubit<NotificationState> {
  NotificationCubit() : super(NotificationInitial()) {
    _loadNotifications();
  }

  List<Notification> _notifications = [];

  /// Load notifications from API
  Future<void> _loadNotifications() async {
    emit(NotificationLoading());
    
    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate API call
      
      // Mock notifications
      _notifications = [
        Notification.questComplete(
          id: '1',
          questTitle: 'Complete Daily Challenge',
          xpReward: 100,
          userId: 'user1',
        ),
        Notification.questAssigned(
          id: '2',
          questTitle: 'Weekly Adventure Quest',
          userId: 'user1',
        ),
        Notification.levelUp(
          id: '3',
          newLevel: 5,
          userId: 'user1',
        ),
        Notification.achievementUnlocked(
          id: '4',
          achievementName: 'First Steps',
          userId: 'user1',
        ),
        Notification.system(
          id: '5',
          title: 'Welcome to Quester!',
          message: 'Start your adventure by completing your first quest.',
        ),
      ];
      
      final unreadCount = _notifications.where((n) => !n.isRead).length;
      
      emit(NotificationLoaded(
        notifications: _notifications,
        unreadCount: unreadCount,
      ));
    } catch (e) {
      emit(NotificationError(message: e.toString()));
    }
  }

  /// Refresh notifications
  Future<void> refreshNotifications() async {
    await _loadNotifications();
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1 && !_notifications[index].isRead) {
        _notifications[index] = _notifications[index].markAsRead();
        
        final unreadCount = _notifications.where((n) => !n.isRead).length;
        
        emit(NotificationLoaded(
          notifications: List.from(_notifications),
          unreadCount: unreadCount,
        ));
      }
    } catch (e) {
      emit(NotificationError(message: e.toString()));
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      _notifications = _notifications.map((n) => n.markAsRead()).toList();
      
      emit(NotificationLoaded(
        notifications: List.from(_notifications),
        unreadCount: 0,
      ));
    } catch (e) {
      emit(NotificationError(message: e.toString()));
    }
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      _notifications.removeWhere((n) => n.id == notificationId);
      
      final unreadCount = _notifications.where((n) => !n.isRead).length;
      
      emit(NotificationLoaded(
        notifications: List.from(_notifications),
        unreadCount: unreadCount,
      ));
    } catch (e) {
      emit(NotificationError(message: e.toString()));
    }
  }

  /// Add new notification (for real-time updates)
  void addNotification(Notification notification) {
    _notifications.insert(0, notification);
    
    final unreadCount = _notifications.where((n) => !n.isRead).length;
    
    emit(NotificationLoaded(
      notifications: List.from(_notifications),
      unreadCount: unreadCount,
    ));
  }

  /// Get unread count
  int get unreadCount {
    final state = this.state;
    if (state is NotificationLoaded) {
      return state.unreadCount;
    }
    return 0;
  }

  /// Get all notifications
  List<Notification> get notifications {
    final state = this.state;
    if (state is NotificationLoaded) {
      return state.notifications;
    }
    return [];
  }

  /// Get unread notifications
  List<Notification> get unreadNotifications {
    return notifications.where((n) => !n.isRead).toList();
  }

  /// Check if notifications are loading
  bool get isLoading => state is NotificationLoading;
}

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:shared/shared.dart';

import '../services/websocket_service.dart';

/// WebSocket connection states
abstract class WebSocketState {}

class WebSocketDisconnected extends WebSocketState {}

class WebSocketConnecting extends WebSocketState {}

class WebSocketConnected extends WebSocketState {}

class WebSocketError extends WebSocketState {
  final String message;

  WebSocketError({required this.message});
}

/// WebSocket management cubit
class WebSocketCubit extends Cubit<WebSocketState> {
  final WebSocketService _webSocketService;

  WebSocketCubit({WebSocketService? webSocketService})
      : _webSocketService = webSocketService ?? WebSocketService(),
        super(WebSocketDisconnected()) {
    _initializeWebSocket();
  }
  StreamSubscription? _connectionSubscription;
  StreamSubscription? _notificationSubscription;

  /// Initialize WebSocket listeners
  void _initializeWebSocket() {
    // Listen to connection state changes
    _connectionSubscription = _webSocketService.connectionStream.listen(
      (isConnected) {
        if (isConnected) {
          emit(WebSocketConnected());
        } else if (_webSocketService.isConnecting) {
          emit(WebSocketConnecting());
        } else {
          emit(WebSocketDisconnected());
        }
      },
    );

    // Listen to notifications
    _notificationSubscription = _webSocketService.notificationStream.listen(
      (notificationData) {
        _handleNotification(notificationData);
      },
    );
  }

  /// Connect to WebSocket server
  Future<void> connect(String authToken) async {
    try {
      emit(WebSocketConnecting());
      await _webSocketService.connect(authToken);
    } catch (error) {
      emit(WebSocketError(message: error.toString()));
    }
  }

  /// Disconnect from WebSocket server
  Future<void> disconnect() async {
    try {
      await _webSocketService.disconnect();
      emit(WebSocketDisconnected());
    } catch (error) {
      emit(WebSocketError(message: error.toString()));
    }
  }

  /// Subscribe to a channel
  void subscribe(String channel) {
    _webSocketService.subscribe(channel);
  }

  /// Unsubscribe from a channel
  void unsubscribe(String channel) {
    _webSocketService.unsubscribe(channel);
  }

  /// Send message through WebSocket
  void sendMessage(Map<String, dynamic> message) {
    _webSocketService.sendMessage(message);
  }

  /// Handle incoming notifications
  void _handleNotification(Map<String, dynamic> notificationData) {
    try {
      // Handle notification data
      final title = notificationData['title'] as String? ?? 'New Notification';
      final message = notificationData['message'] as String? ?? '';
      final type = notificationData['type'] as String? ?? 'general';

      // You can emit events or call other services here
      // For example, update the notification cubit
      debugPrint('📬 Received real-time notification: [$type] $title - $message');

      // TODO: Integrate with NotificationCubit to add the notification
      // context.read<NotificationCubit>().addNotification(notification);

    } catch (error) {
      debugPrint('❌ Failed to handle notification: $error');
    }
  }

  /// Check if WebSocket is connected
  bool get isConnected => _webSocketService.isConnected;

  /// Check if WebSocket is connecting
  bool get isConnecting => _webSocketService.isConnecting;

  /// Get WebSocket message stream
  Stream<Map<String, dynamic>> get messageStream => _webSocketService.messageStream;

  /// Get WebSocket notification stream
  Stream<Map<String, dynamic>> get notificationStream => _webSocketService.notificationStream;

  @override
  Future<void> close() {
    _connectionSubscription?.cancel();
    _notificationSubscription?.cancel();
    _webSocketService.dispose();
    return super.close();
  }
}

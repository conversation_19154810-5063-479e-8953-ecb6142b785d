import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';

import '../providers/auth_cubit.dart';
import '../components/empty_state.dart';
import '../components/loading_overlay.dart';

/// Dashboard screen showing user overview and quick stats
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  void _loadDashboardData() {
    // TODO: Load dashboard data
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, authState) {
          if (authState is AuthLoading) {
            return const LoadingOverlay(
              isLoading: true,
              child: SizedBox.shrink(),
            );
          }

          if (authState is AuthAuthenticated) {
            return _buildDashboard(authState.user);
          }

          return const EmptyState(
            icon: Icons.dashboard_outlined,
            title: 'Welcome to Quester',
            subtitle: 'Please log in to view your dashboard',
          );
        },
      ),
    );
  }

  Widget _buildDashboard(User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome section
          _buildWelcomeSection(user),
          
          const SizedBox(height: 32),
          
          // Stats cards
          _buildStatsSection(),
          
          const SizedBox(height: 32),
          
          // Recent activity
          _buildRecentActivity(),
          
          const SizedBox(height: 32),
          
          // Quick actions
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(User user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Row(
          children: [
            CircleAvatar(
              radius: 32,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: Text(
                user.firstName.substring(0, 1).toUpperCase(),
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back, ${user.firstName}!',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  
                  const SizedBox(height: 4),
                  
                  Text(
                    'Level ${user.level} • ${user.xp} XP',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Stats',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                icon: Icons.assignment_outlined,
                title: 'Active Quests',
                value: '5',
                color: Colors.blue,
              ),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: _buildStatCard(
                icon: Icons.check_circle_outline,
                title: 'Completed',
                value: '23',
                color: Colors.green,
              ),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: _buildStatCard(
                icon: Icons.star_outline,
                title: 'Achievements',
                value: '12',
                color: Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            
            const SizedBox(height: 8),
            
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 4),
            
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        
        const SizedBox(height: 16),
        
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildActivityItem(
                  icon: Icons.check_circle,
                  title: 'Completed "Daily Standup"',
                  subtitle: '2 hours ago',
                  color: Colors.green,
                ),
                
                const Divider(),
                
                _buildActivityItem(
                  icon: Icons.assignment,
                  title: 'Started "Code Review"',
                  subtitle: '4 hours ago',
                  color: Colors.blue,
                ),
                
                const Divider(),
                
                _buildActivityItem(
                  icon: Icons.star,
                  title: 'Earned "Team Player" achievement',
                  subtitle: '1 day ago',
                  color: Colors.orange,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(title),
      subtitle: Text(subtitle),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                icon: Icons.add_task,
                title: 'New Quest',
                onTap: () {
                  // TODO: Navigate to create quest
                },
              ),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: _buildActionCard(
                icon: Icons.leaderboard,
                title: 'Leaderboard',
                onTap: () {
                  // TODO: Navigate to leaderboard
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: Theme.of(context).colorScheme.primary,
              ),
              
              const SizedBox(height: 8),
              
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'dart:io';
import 'dart:convert';
import 'dart:async';

// Import logging
import 'package:logging/logging.dart';

// Import core server packages
import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart' as shelf_io;
import 'package:shelf_router/shelf_router.dart';
import 'package:shelf_web_socket/shelf_web_socket.dart';
import 'package:shelf_static/shelf_static.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

// Import shared package
import 'package:shared/shared.dart';

// Import server services
import '../lib/services/user_repository_impl.dart';
import '../lib/services/quest_repository_impl.dart';
import '../lib/services/auth_service_impl.dart';
import '../lib/services/notification_repository_impl.dart';
import '../lib/services/notification_service_impl.dart';
import '../lib/services/dashboard_repository_impl.dart';
import '../lib/services/dashboard_service_impl.dart';
import '../lib/services/achievement_repository_impl.dart';
import '../lib/services/achievement_service_impl.dart';
import '../lib/services/leaderboard_repository_impl.dart';
import '../lib/services/leaderboard_service_impl.dart';
import '../lib/services/websocket_handler_impl.dart';

// Server configuration using shared constants
class ServerConfig {
  static const String version = AppConstants.appVersion;
  static const String name = 'Quester Server';

  // Environment variables
  static String get host => Platform.environment['HOST'] ?? '0.0.0.0';
  static int get port => int.parse(Platform.environment['PORT'] ?? '8080');
  static String get environment => Platform.environment['NODE_ENV'] ?? 'development';
  static bool get isDevelopment => environment == 'development';
  static bool get isProduction => environment == 'production';
}

// Use WebSocket connection manager from notification service
// (imported from notification_service_impl.dart)

// Main server class
class QuesterServer {
  late final Router _router;
  late final Handler _handler;
  final Logger _logger = Logger('QuesterServer');

  // Repository instances
  late final InMemoryUserRepository _userRepository;
  late final InMemoryQuestRepository _questRepository;
  late final ServerAuthService _authService;

  QuesterServer() {
    _setupLogging();
    _initializeRepositories();
    _setupRouter();
    _setupHandler();
  }

  void _initializeRepositories() {
    _userRepository = InMemoryUserRepository();
    _questRepository = InMemoryQuestRepository();
    final tokenManager = EnhancedTokenManager();
    _authService = ServerAuthService(_userRepository, tokenManager);

    // Initialize with sample data
    _initializeSampleData();
  }

  Future<void> _initializeSampleData() async {
    // Initialize sample quests
    await _questRepository.initializeSampleQuests();

    // Create a sample user
    final sampleUser = User(
      id: 'user_sample_1',
      username: 'demo_user',
      email: '<EMAIL>',
      firstName: 'Demo',
      lastName: 'User',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      isActive: true,
      role: UserRole.user,
      xp: 750,
      level: 2,
    );

    await _userRepository.create(sampleUser);
  }

  void _setupLogging() {
    Logger.root.level = ServerConfig.isDevelopment ? Level.ALL : Level.INFO;
    Logger.root.onRecord.listen((record) {
      print('${record.level.name}: ${record.time}: ${record.message}');
    });
  }

  void _setupRouter() {
    _router = Router()
      // Health check endpoints
      ..get('/', _rootHandler)
      ..get('/health', _healthHandler)
      ..get('/version', _versionHandler)

      // API endpoints
      ..mount('/api/', _createApiRouter())

      // WebSocket endpoints
      ..get('/ws/notifications', webSocketHandler((webSocket) => _handleNotificationWebSocket(webSocket)))
      ..get('/ws/updates', webSocketHandler((webSocket) => _handleUpdatesWebSocket(webSocket)))

      // Static file serving
      ..mount('/static/', createStaticHandler('public', defaultDocument: 'index.html'));
  }

  void _setupHandler() {
    _handler = Pipeline()
        .addMiddleware(_corsMiddleware)
        .addMiddleware(logRequests(logger: _logger.info))
        .addMiddleware(_errorHandler)
        .addMiddleware(_authMiddleware)
        .addHandler(_router.call);
  }

  // CORS middleware
  Middleware get _corsMiddleware => (innerHandler) {
    return (request) async {
      final response = await innerHandler(request);

      return response.change(headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, Content-Type, Accept, Authorization',
        'Access-Control-Max-Age': '86400',
        ...response.headers,
      });
    };
  };

  Router _createApiRouter() {
    return Router()
      // Authentication endpoints
      ..post('/auth/register', _registerHandler)
      ..post('/auth/login', _loginHandler)
      ..post('/auth/logout', _logoutHandler)
      ..post('/auth/refresh', _refreshTokenHandler)

      // User management endpoints
      ..get('/users/me', _getCurrentUserHandler)
      ..get('/users/<id>', _getUserByIdHandler)
      ..put('/users/<id>', _updateUserHandler)
      ..delete('/users/<id>', _deleteUserHandler)

      // Quest management endpoints
      ..get('/quests', _getQuestsHandler)
      ..post('/quests', _createQuestHandler)
      ..get('/quests/<id>', _getQuestByIdHandler)
      ..put('/quests/<id>', _updateQuestHandler)
      ..delete('/quests/<id>', _deleteQuestHandler)
      ..post('/quests/<id>/complete', _completeQuestHandler)

      // Notification endpoints
      ..get('/notifications', _getNotificationsHandler)
      ..post('/notifications', _createNotificationHandler)
      ..put('/notifications/<id>/read', _markNotificationReadHandler)
      ..delete('/notifications/<id>', _deleteNotificationHandler)

      // Dashboard endpoints
      ..get('/dashboard/stats', _getDashboardStatsHandler)
      ..get('/dashboard/activity', _getRecentActivityHandler)

      // Achievement endpoints
      ..get('/achievements', _getAchievementsHandler)
      ..get('/achievements/<id>', _getAchievementByIdHandler)
      ..post('/achievements', _createAchievementHandler)

      // Leaderboard endpoints
      ..get('/leaderboards', _getLeaderboardsHandler)
      ..get('/leaderboards/<id>', _getLeaderboardByIdHandler);
  }

  // Middleware
  Middleware get _errorHandler => (innerHandler) {
    return (request) async {
      try {
        return await innerHandler(request);
      } catch (error, stackTrace) {
        _logger.severe('Unhandled error: $error', error, stackTrace);

        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'message': ServerConfig.isDevelopment
                ? error.toString()
                : 'Internal server error',
            'timestamp': DateTime.now().toIso8601String(),
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
    };
  };

  Middleware get _authMiddleware => (innerHandler) {
    return (request) async {
      // Skip auth for public endpoints
      final publicPaths = [
        '/',
        '/health',
        '/version',
        '/api/auth/register',
        '/api/auth/login',
        '/api/auth/refresh',
      ];

      if (publicPaths.contains(request.url.path) ||
          request.url.path.startsWith('/static/')) {
        return await innerHandler(request);
      }

      // Check for authorization header
      final authHeader = request.headers['authorization'];
      if (authHeader == null || !authHeader.startsWith('Bearer ')) {
        return Response.unauthorized(
          jsonEncode({
            'success': false,
            'message': 'Authorization required',
            'timestamp': DateTime.now().toIso8601String(),
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // TODO: Implement JWT token validation
      // For now, just pass through
      return await innerHandler(request);
    };
  };

  // Basic handlers
  Response _rootHandler(Request request) {
    final data = {
      'name': ServerConfig.name,
      'version': ServerConfig.version,
      'environment': ServerConfig.environment,
      'timestamp': DateTime.now().toIso8601String(),
    };

    return Response.ok(
      jsonEncode({
        'success': true,
        'message': 'Quester Server is running',
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Response _healthHandler(Request request) {
    return Response.ok(
      jsonEncode({
        'success': true,
        'message': 'Server is healthy',
        'data': {
          'status': 'healthy',
          'uptime': DateTime.now().toIso8601String(),
          'connections': EnhancedWebSocketManager.getStats()['totalConnections'],
        },
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Response _versionHandler(Request request) {
    return Response.ok(
      jsonEncode({
        'success': true,
        'data': {
          'version': ServerConfig.version,
          'name': ServerConfig.name,
        },
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // Authentication handlers using shared models
  Future<Response> _registerHandler(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      // Parse registration request using shared model
      final registerRequest = RegisterRequest.fromJson(data);

      // TODO: Implement actual user registration logic
      // For now, create a mock user response
      final user = User(
        id: IdGenerator.generateUserId(),
        username: registerRequest.username,
        email: registerRequest.email,
        firstName: registerRequest.firstName,
        lastName: registerRequest.lastName,
        createdAt: DateTime.now(),
      );

      final authResponse = AuthResponse(
        user: user,
        accessToken: 'mock_access_token_${user.id}',
        refreshToken: 'mock_refresh_token_${user.id}',
      );

      final response = ApiResponse.success(
        data: authResponse,
        message: 'User registered successfully',
      );

      return Response.ok(
        jsonEncode(response.toJson((data) => data.toJson())),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      final errorResponse = ApiResponse.error(
        message: 'Registration failed: ${e.toString()}',
        statusCode: 400,
      );

      return Response.badRequest(
        jsonEncode(errorResponse.toJson((data) => null)),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _loginHandler(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      // Parse login request using shared model
      final loginRequest = LoginRequest.fromJson(data);

      // TODO: Implement actual authentication logic
      // For now, create a mock user response
      final user = User(
        id: IdGenerator.generateUserId(),
        username: 'mock_user',
        email: loginRequest.email,
        firstName: 'Mock',
        lastName: 'User',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        lastLoginAt: DateTime.now(),
      );

      final authResponse = AuthResponse(
        user: user,
        accessToken: 'mock_access_token_${user.id}',
        refreshToken: 'mock_refresh_token_${user.id}',
      );

      final response = ApiResponse.success(
        data: authResponse,
        message: 'Login successful',
      );

      return Response.ok(
        jsonEncode(response.toJson((data) => data.toJson())),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      final errorResponse = ApiResponse.error(
        message: 'Login failed: ${e.toString()}',
        statusCode: 401,
      );

      return Response(
        401,
        body: jsonEncode(errorResponse.toJson((data) => data)),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _logoutHandler(Request request) async {
    try {
      // TODO: Implement actual logout logic (invalidate tokens, etc.)

      final response = ApiResponse.success(
        data: null,
        message: 'Logout successful',
      );

      return Response.ok(
        jsonEncode(response.toJson((data) => data)),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      final errorResponse = ApiResponse.error(
        message: 'Logout failed: ${e.toString()}',
        statusCode: 500,
      );

      return Response.internalServerError(
        body: jsonEncode(errorResponse.toJson((data) => data)),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _refreshTokenHandler(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      final refreshToken = data['refreshToken'] as String?;
      if (refreshToken == null) {
        final errorResponse = ApiResponse.error(
          message: 'Refresh token is required',
          statusCode: 400,
        );

        return Response(
          400,
          body: jsonEncode(errorResponse.toJson((data) => data)),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // TODO: Implement actual token refresh logic
      final tokenResponse = TokenResponse(
        accessToken: 'new_mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
        refreshToken: 'new_mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
      );

      final response = ApiResponse.success(
        data: tokenResponse,
        message: 'Token refreshed successfully',
      );

      return Response.ok(
        jsonEncode(response.toJson((data) => data.toJson())),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      final errorResponse = ApiResponse.error(
        message: 'Token refresh failed: ${e.toString()}',
        statusCode: 401,
      );

      return Response(
        401,
        body: jsonEncode(errorResponse.toJson((data) => data)),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  // User handlers using shared models
  Future<Response> _getCurrentUserHandler(Request request) async {
    try {
      // TODO: Extract user ID from JWT token
      // For now, return a mock user
      final user = User(
        id: 'user_current_123',
        username: 'current_user',
        email: '<EMAIL>',
        firstName: 'Current',
        lastName: 'User',
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        lastLoginAt: DateTime.now().subtract(const Duration(minutes: 5)),
        xp: 1250,
        level: 3,
      );

      final response = ApiResponse.success(
        data: user,
        message: 'User retrieved successfully',
      );

      return Response.ok(
        jsonEncode(response.toJson((data) => data.toJson())),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      final errorResponse = ApiResponse.error(
        message: 'Failed to get user: ${e.toString()}',
        statusCode: 500,
      );

      return Response(
        500,
        body: jsonEncode(errorResponse.toJson((data) => data)),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getUserByIdHandler(Request request) async {
    try {
      final userId = request.params['id'];
      if (userId == null) {
        final errorResponse = ApiResponse.error(
          message: 'User ID is required',
          statusCode: 400,
        );

        return Response(
          400,
          body: jsonEncode(errorResponse.toJson((data) => data)),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // TODO: Implement actual user lookup
      final user = User(
        id: userId,
        username: 'user_$userId',
        email: 'user$<EMAIL>',
        firstName: 'User',
        lastName: userId,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        xp: 500,
        level: 2,
      );

      final response = ApiResponse.success(
        data: user,
        message: 'User retrieved successfully',
      );

      return Response.ok(
        jsonEncode(response.toJson((data) => data.toJson())),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      final errorResponse = ApiResponse.error(
        message: 'Failed to get user: ${e.toString()}',
        statusCode: 500,
      );

      return Response(
        500,
        body: jsonEncode(errorResponse.toJson((data) => data)),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _updateUserHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Update user endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _deleteUserHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Delete user endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // Quest handlers using shared models
  Future<Response> _getQuestsHandler(Request request) async {
    try {
      // TODO: Implement actual quest retrieval from database
      // For now, return mock quests
      final quests = [
        Quest(
          id: 'quest_1',
          title: 'Complete Daily Exercise',
          description: 'Exercise for at least 30 minutes today',
          difficulty: QuestDifficulty.easy,
          category: QuestCategory.daily,
          status: QuestStatus.inProgress,
          xpReward: 50,
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          expiresAt: DateTime.now().add(const Duration(hours: 12)),
        ),
        Quest(
          id: 'quest_2',
          title: 'Learn New Programming Concept',
          description: 'Study and implement a new programming concept or pattern',
          difficulty: QuestDifficulty.medium,
          category: QuestCategory.challenge,
          status: QuestStatus.inProgress,
          xpReward: 100,
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
          expiresAt: DateTime.now().add(const Duration(days: 3)),
        ),
      ];

      final response = ApiResponse.success(
        data: quests,
        message: 'Quests retrieved successfully',
      );

      return Response.ok(
        jsonEncode(response.toJson((data) => data.map((q) => q.toJson()).toList())),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      final errorResponse = ApiResponse.error(
        message: 'Failed to get quests: ${e.toString()}',
        statusCode: 500,
      );

      return Response(
        500,
        body: jsonEncode(errorResponse.toJson((data) => data)),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _createQuestHandler(Request request) async {
    try {
      final body = await request.readAsString();
      final data = jsonDecode(body) as Map<String, dynamic>;

      // Parse create quest request using shared model
      final createRequest = CreateQuestRequest.fromJson(data);

      // TODO: Implement actual quest creation logic
      final quest = Quest.create(
        id: IdGenerator.generateQuestId(),
        title: createRequest.title,
        description: createRequest.description,
        difficulty: QuestDifficulty.values.firstWhere(
          (d) => d.name == createRequest.difficulty,
          orElse: () => QuestDifficulty.easy,
        ),
        category: createRequest.category != null
            ? QuestCategory.values.firstWhere(
                (c) => c.name == createRequest.category,
                orElse: () => QuestCategory.challenge,
              )
            : QuestCategory.challenge,
        xpReward: createRequest.xpReward,
        coinReward: createRequest.coinReward,
        expiresAt: createRequest.dueDate,
      );

      final response = ApiResponse.success(
        data: quest,
        message: 'Quest created successfully',
      );

      return Response.ok(
        jsonEncode(response.toJson((data) => data.toJson())),
        headers: {'Content-Type': 'application/json'},
      );
    } catch (e) {
      final errorResponse = ApiResponse.error(
        message: 'Failed to create quest: ${e.toString()}',
        statusCode: 400,
      );

      return Response(
        400,
        body: jsonEncode(errorResponse.toJson((data) => data)),
        headers: {'Content-Type': 'application/json'},
      );
    }
  }

  Future<Response> _getQuestByIdHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get quest by ID endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _updateQuestHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Update quest endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _deleteQuestHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Delete quest endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _completeQuestHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Complete quest endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // Notification handlers
  Future<Response> _getNotificationsHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get notifications endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _createNotificationHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Create notification endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _markNotificationReadHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Mark notification read endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _deleteNotificationHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Delete notification endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // Dashboard handlers
  Future<Response> _getDashboardStatsHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get dashboard stats endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _getRecentActivityHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get recent activity endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // Achievement handlers
  Future<Response> _getAchievementsHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get achievements endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _getAchievementByIdHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get achievement by ID endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _createAchievementHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Create achievement endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // Leaderboard handlers
  Future<Response> _getLeaderboardsHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get leaderboards endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _getLeaderboardByIdHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get leaderboard by ID endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // WebSocket handlers
  void _handleNotificationWebSocket(WebSocketChannel webSocket) {
    final connectionId = DateTime.now().millisecondsSinceEpoch.toString();
    EnhancedWebSocketManager.addConnection(connectionId, webSocket);

    webSocket.stream.listen(
      (message) {
        _logger.info('Received WebSocket message: $message');
        // Handle incoming WebSocket messages
      },
      onDone: () {
        EnhancedWebSocketManager.removeConnection(connectionId);
        _logger.info('WebSocket connection closed: $connectionId');
      },
      onError: (error) {
        _logger.warning('WebSocket error: $error');
        EnhancedWebSocketManager.removeConnection(connectionId);
      },
    );
  }

  void _handleUpdatesWebSocket(WebSocketChannel webSocket) {
    final connectionId = DateTime.now().millisecondsSinceEpoch.toString();
    EnhancedWebSocketManager.addConnection(connectionId, webSocket);

    webSocket.stream.listen(
      (message) {
        _logger.info('Received WebSocket message: $message');
        // Handle incoming WebSocket messages
      },
      onDone: () {
        EnhancedWebSocketManager.removeConnection(connectionId);
        _logger.info('WebSocket connection closed: $connectionId');
      },
      onError: (error) {
        _logger.warning('WebSocket error: $error');
        EnhancedWebSocketManager.removeConnection(connectionId);
      },
    );
  }

  // Start the server
  Future<void> start() async {
    try {
      final server = await shelf_io.serve(_handler, ServerConfig.host, ServerConfig.port);
      _logger.info('${ServerConfig.name} v${ServerConfig.version} listening on ${server.address.host}:${server.port}');
      _logger.info('Environment: ${ServerConfig.environment}');
    } catch (error, stackTrace) {
      _logger.severe('Failed to start server: $error', error, stackTrace);
      rethrow;
    }
  }
}

// Main entry point
void main(List<String> args) async {
  final server = QuesterServer();
  await server.start();
}

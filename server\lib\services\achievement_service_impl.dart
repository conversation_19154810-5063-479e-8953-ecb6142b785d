import 'dart:async';
import 'package:shared/shared.dart';
import 'achievement_repository_impl.dart';
import 'user_repository_impl.dart';
import 'quest_repository_impl.dart';
import 'notification_service_impl.dart';

/// Server-side achievement service implementation
class ServerAchievementService {
  final InMemoryAchievementRepository _achievementRepository;
  final InMemoryUserRepository _userRepository;
  final InMemoryQuestRepository _questRepository;
  final ServerNotificationService _notificationService;
  
  // Achievement progress tracking
  final Map<String, Map<String, dynamic>> _userProgressCache = {}; // userId -> progress data

  ServerAchievementService({
    required InMemoryAchievementRepository achievementRepository,
    required InMemoryUserRepository userRepository,
    required InMemoryQuestRepository questRepository,
    required ServerNotificationService notificationService,
  })  : _achievementRepository = achievementRepository,
        _userRepository = userRepository,
        _questRepository = questRepository,
        _notificationService = notificationService;

  /// Get all achievements
  Future<List<Achievement>> getAllAchievements({int page = 1, int pageSize = 20}) async {
    return await _achievementRepository.getAll(page: page, pageSize: pageSize);
  }

  /// Get achievements by category
  Future<List<Achievement>> getAchievementsByCategory(AchievementCategory category, {int page = 1, int pageSize = 20}) async {
    return await _achievementRepository.getByCategory(category.name, page: page, pageSize: pageSize);
  }

  /// Get user's achievements
  Future<List<Achievement>> getUserAchievements(String userId, {int page = 1, int pageSize = 20}) async {
    return await _achievementRepository.getUserAchievements(userId, page: page, pageSize: pageSize);
  }

  /// Get available achievements for user
  Future<List<Achievement>> getAvailableAchievements(String userId, {int page = 1, int pageSize = 20}) async {
    return await _achievementRepository.getAvailableForUser(userId, page: page, pageSize: pageSize);
  }

  /// Get user achievement progress
  Future<List<UserAchievement>> getUserAchievementProgress(String userId) async {
    return await _achievementRepository.getAllUserAchievements(userId);
  }

  /// Get achievement summary for user
  Future<AchievementSummary> getAchievementSummary(String userId) async {
    final allAchievements = await _achievementRepository.getAll();
    final userAchievements = await _achievementRepository.getAllUserAchievements(userId);
    
    final totalAchievements = allAchievements.length;
    final unlockedAchievements = userAchievements.where((ua) => ua.status == AchievementStatus.unlocked).length;
    final inProgressAchievements = userAchievements.where((ua) => ua.status == AchievementStatus.inProgress).length;
    final lockedAchievements = totalAchievements - unlockedAchievements - inProgressAchievements;
    
    final completionPercentage = totalAchievements > 0 ? (unlockedAchievements / totalAchievements) * 100 : 0.0;
    
    // Calculate total rewards earned
    int totalXpEarned = 0;
    int totalCoinsEarned = 0;
    
    for (final userAchievement in userAchievements) {
      if (userAchievement.status == AchievementStatus.unlocked && userAchievement.achievement != null) {
        totalXpEarned += userAchievement.achievement!.xpReward;
        totalCoinsEarned += userAchievement.achievement!.coinReward ?? 0;
      }
    }
    
    // Get recent unlocked achievements
    final recentUnlocked = userAchievements
        .where((ua) => ua.status == AchievementStatus.unlocked && ua.unlockedAt != null)
        .toList()
      ..sort((a, b) => b.unlockedAt!.compareTo(a.unlockedAt!));
    
    final recentUnlockedLimited = recentUnlocked.take(5).toList();
    
    // Category breakdown
    final categoryBreakdown = <AchievementCategory, int>{};
    for (final category in AchievementCategory.values) {
      categoryBreakdown[category] = userAchievements
          .where((ua) => ua.achievement?.category == category && ua.status == AchievementStatus.unlocked)
          .length;
    }
    
    return AchievementSummary(
      totalAchievements: totalAchievements,
      unlockedAchievements: unlockedAchievements,
      inProgressAchievements: inProgressAchievements,
      lockedAchievements: lockedAchievements,
      completionPercentage: completionPercentage,
      totalXpEarned: totalXpEarned,
      totalCoinsEarned: totalCoinsEarned,
      recentUnlocked: recentUnlockedLimited,
      categoryBreakdown: categoryBreakdown,
    );
  }

  /// Check and update achievement progress for user
  Future<List<Achievement>> checkAchievementProgress(String userId, {String? eventType, Map<String, dynamic>? eventData}) async {
    final user = await _userRepository.getById(userId);
    if (user == null) return [];
    
    final allAchievements = await _achievementRepository.getAll();
    final newlyUnlocked = <Achievement>[];
    
    for (final achievement in allAchievements) {
      if (!achievement.isAvailable) continue;
      
      // Check if user already has this achievement (and it's not repeatable)
      if (!achievement.isRepeatable && await _achievementRepository.userHasAchievement(userId, achievement.id)) {
        continue;
      }
      
      // Check progress for each requirement
      bool allRequirementsMet = true;
      double totalProgress = 0.0;
      final progressData = <String, dynamic>{};
      
      for (final requirement in achievement.requirements) {
        final progress = await _checkRequirementProgress(userId, requirement, user);
        progressData[requirement.id] = progress;
        
        final requirementProgress = progress['progress'] as double;
        totalProgress += requirementProgress;
        
        if (requirementProgress < 1.0) {
          allRequirementsMet = false;
        }
      }
      
      // Calculate overall progress
      final overallProgress = achievement.requirements.isNotEmpty 
          ? totalProgress / achievement.requirements.length 
          : 0.0;
      
      // Update progress in repository
      await _achievementRepository.updateProgressForUser(userId, achievement.id, {
        'progress': overallProgress,
        'progressData': progressData,
      });
      
      // If all requirements are met, unlock the achievement
      if (allRequirementsMet && overallProgress >= 1.0) {
        await _unlockAchievement(userId, achievement);
        newlyUnlocked.add(achievement);
      }
    }
    
    return newlyUnlocked;
  }

  /// Handle quest completion event
  Future<List<Achievement>> handleQuestCompletion(String userId, String questId) async {
    // Update user progress cache
    if (!_userProgressCache.containsKey(userId)) {
      _userProgressCache[userId] = {};
    }
    
    final userQuests = await _questRepository.getAssignedToUser(userId);
    final completedQuests = userQuests.where((q) => q.status == QuestStatus.completed).length;
    
    _userProgressCache[userId]!['completedQuests'] = completedQuests;
    
    return await checkAchievementProgress(userId, eventType: 'quest_completion', eventData: {
      'questId': questId,
      'completedQuests': completedQuests,
    });
  }

  /// Handle level up event
  Future<List<Achievement>> handleLevelUp(String userId, int newLevel) async {
    // Update user progress cache
    if (!_userProgressCache.containsKey(userId)) {
      _userProgressCache[userId] = {};
    }
    
    _userProgressCache[userId]!['level'] = newLevel;
    
    return await checkAchievementProgress(userId, eventType: 'level_up', eventData: {
      'newLevel': newLevel,
    });
  }

  /// Handle XP earned event
  Future<List<Achievement>> handleXpEarned(String userId, int totalXp) async {
    // Update user progress cache
    if (!_userProgressCache.containsKey(userId)) {
      _userProgressCache[userId] = {};
    }
    
    _userProgressCache[userId]!['totalXp'] = totalXp;
    
    return await checkAchievementProgress(userId, eventType: 'xp_earned', eventData: {
      'totalXp': totalXp,
    });
  }

  /// Check progress for a specific requirement
  Future<Map<String, dynamic>> _checkRequirementProgress(String userId, AchievementRequirement requirement, User user) async {
    switch (requirement.type) {
      case AchievementRequirementType.questCompletion:
        final userQuests = await _questRepository.getAssignedToUser(userId);
        final completedQuests = userQuests.where((q) => q.status == QuestStatus.completed).length;
        final progress = (completedQuests / requirement.targetValue).clamp(0.0, 1.0);
        
        return {
          'progress': progress,
          'current': completedQuests,
          'target': requirement.targetValue,
          'type': 'quest_completion',
        };
        
      case AchievementRequirementType.levelReached:
        final progress = (user.level / requirement.targetValue).clamp(0.0, 1.0);
        
        return {
          'progress': progress,
          'current': user.level,
          'target': requirement.targetValue,
          'type': 'level_reached',
        };
        
      case AchievementRequirementType.xpEarned:
        final progress = (user.xp / requirement.targetValue).clamp(0.0, 1.0);
        
        return {
          'progress': progress,
          'current': user.xp,
          'target': requirement.targetValue,
          'type': 'xp_earned',
        };
        
      case AchievementRequirementType.streakMaintained:
        // Would need to implement streak tracking
        return {
          'progress': 0.0,
          'current': 0,
          'target': requirement.targetValue,
          'type': 'streak_maintained',
        };
        
      case AchievementRequirementType.timeSpent:
        // Would need to implement time tracking
        return {
          'progress': 0.0,
          'current': 0,
          'target': requirement.targetValue,
          'type': 'time_spent',
        };
        
      case AchievementRequirementType.socialInteraction:
        // Would need to implement social interaction tracking
        return {
          'progress': 0.0,
          'current': 0,
          'target': requirement.targetValue,
          'type': 'social_interaction',
        };
        
      case AchievementRequirementType.custom:
        // Handle custom requirements based on criteria
        final criteria = requirement.criteria;
        // This would be implemented based on specific custom requirements
        return {
          'progress': 0.0,
          'current': 0,
          'target': requirement.targetValue,
          'type': 'custom',
          'criteria': criteria,
        };
    }
  }

  /// Unlock achievement for user
  Future<void> _unlockAchievement(String userId, Achievement achievement) async {
    // Award the achievement
    await _achievementRepository.awardToUser(achievement.id, userId);
    
    // Update user XP and coins
    final user = await _userRepository.getById(userId);
    if (user != null) {
      final updatedUser = User(
        id: user.id,
        username: user.username,
        email: user.email,
        passwordHash: user.passwordHash,
        firstName: user.firstName,
        lastName: user.lastName,
        avatarUrl: user.avatarUrl,
        xp: user.xp + achievement.xpReward,
        level: user.level, // Level calculation would be done elsewhere
        coins: user.coins + (achievement.coinReward ?? 0),
        createdAt: user.createdAt,
        updatedAt: DateTime.now(),
        lastLoginAt: user.lastLoginAt,
        isActive: user.isActive,
        preferences: user.preferences,
        metadata: user.metadata,
      );
      
      await _userRepository.update(userId, updatedUser);
    }
    
    // Send notification
    await _notificationService.sendAchievementUnlockedNotification(
      userId: userId,
      achievementTitle: achievement.title,
      achievementId: achievement.id,
    );
  }

  /// Initialize sample achievements
  Future<void> initializeSampleAchievements() async {
    await _achievementRepository.initializeSampleAchievements();
  }

  /// Get achievement by ID
  Future<Achievement?> getAchievementById(String achievementId) async {
    return await _achievementRepository.getById(achievementId);
  }

  /// Create new achievement
  Future<Achievement> createAchievement(Achievement achievement) async {
    return await _achievementRepository.create(achievement);
  }

  /// Update achievement
  Future<Achievement> updateAchievement(String achievementId, Achievement achievement) async {
    return await _achievementRepository.update(achievementId, achievement);
  }

  /// Delete achievement
  Future<void> deleteAchievement(String achievementId) async {
    await _achievementRepository.delete(achievementId);
  }
}

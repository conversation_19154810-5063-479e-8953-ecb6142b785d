import 'dart:async';
import 'package:shared/shared.dart';

/// In-memory implementation of NotificationRepository for development/testing
class InMemoryNotificationRepository implements NotificationRepository {
  final Map<String, Notification> _notifications = {};
  final Map<String, List<String>> _userNotifications = {}; // userId -> notificationIds

  @override
  Future<Notification> create(Notification entity) async {
    _notifications[entity.id] = entity;
    
    // Add to user notifications if userId is specified
    if (entity.userId != null) {
      if (!_userNotifications.containsKey(entity.userId)) {
        _userNotifications[entity.userId!] = [];
      }
      _userNotifications[entity.userId!]!.add(entity.id);
    }
    
    return entity;
  }

  @override
  Future<Notification?> getById(String id) async {
    return _notifications[id];
  }

  @override
  Future<List<Notification>> getAll({int page = 1, int pageSize = 20}) async {
    final notifications = _notifications.values.toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= notifications.length) return [];
    
    return notifications.sublist(
      startIndex,
      endIndex > notifications.length ? notifications.length : endIndex,
    );
  }

  @override
  Future<Notification> update(String id, Notification entity) async {
    if (!_notifications.containsKey(id)) {
      throw Exception('Notification not found');
    }
    _notifications[id] = entity;
    return entity;
  }

  @override
  Future<void> delete(String id) async {
    final notification = _notifications.remove(id);
    
    // Remove from user notifications
    if (notification?.userId != null) {
      _userNotifications[notification!.userId]?.remove(id);
    }
  }

  @override
  Future<bool> exists(String id) async {
    return _notifications.containsKey(id);
  }

  @override
  Future<int> count() async {
    return _notifications.length;
  }

  @override
  Future<List<Notification>> search(String query, {int page = 1, int pageSize = 20}) async {
    final notifications = _notifications.values.where((notification) =>
        notification.title.toLowerCase().contains(query.toLowerCase()) ||
        notification.message.toLowerCase().contains(query.toLowerCase())
    ).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= notifications.length) return [];
    
    return notifications.sublist(
      startIndex,
      endIndex > notifications.length ? notifications.length : endIndex,
    );
  }

  @override
  Future<List<Notification>> getFiltered(Map<String, dynamic> filters, {int page = 1, int pageSize = 20}) async {
    var notifications = _notifications.values.toList();
    
    // Apply filters
    if (filters.containsKey('type')) {
      final type = NotificationType.values.firstWhere(
        (t) => t.name == filters['type'],
        orElse: () => NotificationType.info,
      );
      notifications = notifications.where((notification) => notification.type == type).toList();
    }
    
    if (filters.containsKey('priority')) {
      final priority = NotificationPriority.values.firstWhere(
        (p) => p.name == filters['priority'],
        orElse: () => NotificationPriority.normal,
      );
      notifications = notifications.where((notification) => notification.priority == priority).toList();
    }
    
    if (filters.containsKey('isRead')) {
      final isRead = filters['isRead'] as bool;
      notifications = notifications.where((notification) => 
        isRead ? notification.readAt != null : notification.readAt == null
      ).toList();
    }
    
    if (filters.containsKey('userId')) {
      final userId = filters['userId'] as String;
      notifications = notifications.where((notification) => notification.userId == userId).toList();
    }
    
    notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= notifications.length) return [];
    
    return notifications.sublist(
      startIndex,
      endIndex > notifications.length ? notifications.length : endIndex,
    );
  }

  @override
  Future<List<Notification>> createBatch(List<Notification> entities) async {
    for (final notification in entities) {
      await create(notification);
    }
    return entities;
  }

  @override
  Future<List<Notification>> updateBatch(List<Notification> entities) async {
    for (final notification in entities) {
      if (_notifications.containsKey(notification.id)) {
        _notifications[notification.id] = notification;
      }
    }
    return entities;
  }

  @override
  Future<void> deleteBatch(List<String> ids) async {
    for (final id in ids) {
      await delete(id);
    }
  }

  @override
  Future<List<Notification>> getForUser(String userId, {int page = 1, int pageSize = 20}) async {
    final notificationIds = _userNotifications[userId] ?? [];
    final notifications = notificationIds
        .map((id) => _notifications[id])
        .where((notification) => notification != null)
        .cast<Notification>()
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= notifications.length) return [];
    
    return notifications.sublist(
      startIndex,
      endIndex > notifications.length ? notifications.length : endIndex,
    );
  }

  @override
  Future<List<Notification>> getUnreadForUser(String userId, {int page = 1, int pageSize = 20}) async {
    final notificationIds = _userNotifications[userId] ?? [];
    final notifications = notificationIds
        .map((id) => _notifications[id])
        .where((notification) => notification != null && notification!.readAt == null)
        .cast<Notification>()
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= notifications.length) return [];
    
    return notifications.sublist(
      startIndex,
      endIndex > notifications.length ? notifications.length : endIndex,
    );
  }

  @override
  Future<List<Notification>> getByType(NotificationType type, {int page = 1, int pageSize = 20}) async {
    final notifications = _notifications.values
        .where((notification) => notification.type == type)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= notifications.length) return [];
    
    return notifications.sublist(
      startIndex,
      endIndex > notifications.length ? notifications.length : endIndex,
    );
  }

  @override
  Future<void> markAsRead(String notificationId) async {
    final notification = _notifications[notificationId];
    if (notification != null) {
      final updatedNotification = Notification(
        id: notification.id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        createdAt: notification.createdAt,
        readAt: DateTime.now(),
        expiresAt: notification.expiresAt,
        userId: notification.userId,
        actionUrl: notification.actionUrl,
        metadata: notification.metadata,
        isPersistent: notification.isPersistent,
      );
      _notifications[notificationId] = updatedNotification;
    }
  }

  @override
  Future<void> markAllAsReadForUser(String userId) async {
    final notificationIds = _userNotifications[userId] ?? [];
    for (final id in notificationIds) {
      await markAsRead(id);
    }
  }

  @override
  Future<int> getUnreadCountForUser(String userId) async {
    final notificationIds = _userNotifications[userId] ?? [];
    return notificationIds
        .map((id) => _notifications[id])
        .where((notification) => notification != null && notification!.readAt == null)
        .length;
  }

  @override
  Future<void> deleteOldNotifications(Duration olderThan) async {
    final cutoffDate = DateTime.now().subtract(olderThan);
    final idsToDelete = _notifications.entries
        .where((entry) => entry.value.createdAt.isBefore(cutoffDate))
        .map((entry) => entry.key)
        .toList();
    
    await deleteBatch(idsToDelete);
  }

  @override
  Future<Notification> sendToUser(String userId, String title, String message, {NotificationType? type, Map<String, dynamic>? data}) async {
    final notification = Notification(
      id: IdGenerator.generateNotificationId(),
      title: title,
      message: message,
      type: type ?? NotificationType.info,
      createdAt: DateTime.now(),
      userId: userId,
      metadata: data,
    );
    
    return await create(notification);
  }

  @override
  Future<List<Notification>> sendToUsers(List<String> userIds, String title, String message, {NotificationType? type, Map<String, dynamic>? data}) async {
    final notifications = <Notification>[];
    
    for (final userId in userIds) {
      final notification = await sendToUser(userId, title, message, type: type, data: data);
      notifications.add(notification);
    }
    
    return notifications;
  }

  @override
  Future<List<Notification>> sendBroadcast(String title, String message, {NotificationType? type, Map<String, dynamic>? data}) async {
    // For broadcast, we'll create a notification without a specific userId
    final notification = Notification(
      id: IdGenerator.generateNotificationId(),
      title: title,
      message: message,
      type: type ?? NotificationType.system,
      createdAt: DateTime.now(),
      metadata: data,
      isPersistent: true,
    );
    
    await create(notification);
    return [notification];
  }
}

import 'dart:async';
import 'package:shared/shared.dart';

/// In-memory implementation of UserRepository for development/testing
class InMemoryUserRepository implements UserRepository {
  final Map<String, User> _users = {};
  final Map<String, List<UserActivity>> _userActivities = {};

  @override
  Future<User> create(User entity) async {
    _users[entity.id] = entity;
    return entity;
  }

  @override
  Future<User?> getById(String id) async {
    return _users[id];
  }

  @override
  Future<List<User>> getAll({int page = 1, int pageSize = 20}) async {
    final users = _users.values.toList();
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= users.length) return [];
    
    return users.sublist(
      startIndex,
      endIndex > users.length ? users.length : endIndex,
    );
  }

  @override
  Future<User> update(String id, User entity) async {
    if (!_users.containsKey(id)) {
      throw Exception('User not found');
    }
    _users[id] = entity;
    return entity;
  }

  @override
  Future<void> delete(String id) async {
    _users.remove(id);
    _userActivities.remove(id);
  }

  @override
  Future<bool> exists(String id) async {
    return _users.containsKey(id);
  }

  @override
  Future<int> count() async {
    return _users.length;
  }

  @override
  Future<List<User>> search(String query, {int page = 1, int pageSize = 20}) async {
    final users = _users.values.where((user) =>
        user.username.toLowerCase().contains(query.toLowerCase()) ||
        user.email.toLowerCase().contains(query.toLowerCase()) ||
        (user.firstName?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
        (user.lastName?.toLowerCase().contains(query.toLowerCase()) ?? false)
    ).toList();
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= users.length) return [];
    
    return users.sublist(
      startIndex,
      endIndex > users.length ? users.length : endIndex,
    );
  }

  @override
  Future<List<User>> getFiltered(Map<String, dynamic> filters, {int page = 1, int pageSize = 20}) async {
    var users = _users.values.toList();
    
    // Apply filters
    if (filters.containsKey('role')) {
      final role = UserRole.values.firstWhere(
        (r) => r.name == filters['role'],
        orElse: () => UserRole.user,
      );
      users = users.where((user) => user.role == role).toList();
    }
    
    if (filters.containsKey('isActive')) {
      final isActive = filters['isActive'] as bool;
      users = users.where((user) => user.isActive == isActive).toList();
    }
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= users.length) return [];
    
    return users.sublist(
      startIndex,
      endIndex > users.length ? users.length : endIndex,
    );
  }

  @override
  Future<List<User>> createBatch(List<User> entities) async {
    for (final user in entities) {
      _users[user.id] = user;
    }
    return entities;
  }

  @override
  Future<List<User>> updateBatch(List<User> entities) async {
    for (final user in entities) {
      if (_users.containsKey(user.id)) {
        _users[user.id] = user;
      }
    }
    return entities;
  }

  @override
  Future<void> deleteBatch(List<String> ids) async {
    for (final id in ids) {
      _users.remove(id);
      _userActivities.remove(id);
    }
  }

  @override
  Future<User?> getByEmail(String email) async {
    try {
      return _users.values.firstWhere(
        (user) => user.email == email,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<User?> getByUsername(String username) async {
    try {
      return _users.values.firstWhere(
        (user) => user.username == username,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<User>> getByRole(UserRole role, {int page = 1, int pageSize = 20}) async {
    final users = _users.values.where((user) => user.role == role).toList();
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= users.length) return [];
    
    return users.sublist(
      startIndex,
      endIndex > users.length ? users.length : endIndex,
    );
  }

  @override
  Future<List<User>> getActiveUsers({int page = 1, int pageSize = 20}) async {
    final users = _users.values.where((user) => user.isActive).toList();
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= users.length) return [];
    
    return users.sublist(
      startIndex,
      endIndex > users.length ? users.length : endIndex,
    );
  }

  @override
  Future<User> updateProfile(String userId, Map<String, dynamic> updates) async {
    final user = _users[userId];
    if (user == null) {
      throw Exception('User not found');
    }
    
    final updatedUser = user.copyWith(
      firstName: updates['firstName'] ?? user.firstName,
      lastName: updates['lastName'] ?? user.lastName,
      bio: updates['bio'] ?? user.bio,
      avatarUrl: updates['avatarUrl'] ?? user.avatarUrl,
    );
    
    _users[userId] = updatedUser;
    return updatedUser;
  }

  @override
  Future<User> updateAvatar(String userId, String avatarUrl) async {
    final user = _users[userId];
    if (user == null) {
      throw Exception('User not found');
    }
    
    final updatedUser = user.copyWith(avatarUrl: avatarUrl);
    _users[userId] = updatedUser;
    return updatedUser;
  }

  @override
  Future<void> deactivateUser(String userId) async {
    final user = _users[userId];
    if (user == null) {
      throw Exception('User not found');
    }
    
    final updatedUser = user.copyWith(isActive: false);
    _users[userId] = updatedUser;
  }

  @override
  Future<void> activateUser(String userId) async {
    final user = _users[userId];
    if (user == null) {
      throw Exception('User not found');
    }
    
    final updatedUser = user.copyWith(isActive: true);
    _users[userId] = updatedUser;
  }

  @override
  Future<Map<String, dynamic>> getUserStats(String userId) async {
    final user = _users[userId];
    if (user == null) {
      throw Exception('User not found');
    }
    
    final activities = _userActivities[userId] ?? [];
    
    return {
      'totalXp': user.xp,
      'level': user.level,
      'activitiesCount': activities.length,
      'joinDate': user.createdAt.toIso8601String(),
      'lastLogin': user.lastLoginAt?.toIso8601String(),
    };
  }

  @override
  Future<List<UserActivity>> getUserActivity(String userId, {int page = 1, int pageSize = 20}) async {
    final activities = _userActivities[userId] ?? [];
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= activities.length) return [];
    
    return activities.sublist(
      startIndex,
      endIndex > activities.length ? activities.length : endIndex,
    );
  }

  /// Add activity for a user (helper method)
  Future<void> addUserActivity(String userId, UserActivity activity) async {
    if (!_userActivities.containsKey(userId)) {
      _userActivities[userId] = [];
    }
    _userActivities[userId]!.add(activity);
  }
}

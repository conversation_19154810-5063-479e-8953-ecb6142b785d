import 'dart:async';
import 'dart:convert';
import 'package:shared/shared.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'auth_service_impl.dart';
import 'notification_service_impl.dart';
import 'dashboard_service_impl.dart';
import 'leaderboard_service_impl.dart';

/// Enhanced WebSocket connection manager with proper message handling
class EnhancedWebSocketManager {
  static final Map<String, WebSocketConnection> _connections = {};
  static final Map<String, String> _userConnections = {}; // userId -> connectionId
  static final Map<String, Set<String>> _channelSubscriptions = {}; // channel -> connectionIds
  
  /// Add a WebSocket connection
  static void addConnection(String connectionId, WebSocketChannel channel, {String? userId, String? sessionId}) {
    final connection = WebSocketConnection(
      id: connectionId,
      channel: channel,
      userId: userId,
      sessionId: sessionId,
      connectedAt: DateTime.now(),
      lastActivity: DateTime.now(),
    );
    
    _connections[connectionId] = connection;
    
    if (userId != null) {
      _userConnections[userId] = connectionId;
    }
  }
  
  /// Remove a WebSocket connection
  static void removeConnection(String connectionId) {
    final connection = _connections.remove(connectionId);
    if (connection?.userId != null) {
      _userConnections.remove(connection!.userId);
    }
    
    // Remove from all channel subscriptions
    _channelSubscriptions.forEach((channel, connectionIds) {
      connectionIds.remove(connectionId);
    });
  }
  
  /// Subscribe connection to a channel
  static void subscribeToChannel(String connectionId, String channel) {
    if (!_channelSubscriptions.containsKey(channel)) {
      _channelSubscriptions[channel] = <String>{};
    }
    _channelSubscriptions[channel]!.add(connectionId);
  }
  
  /// Unsubscribe connection from a channel
  static void unsubscribeFromChannel(String connectionId, String channel) {
    _channelSubscriptions[channel]?.remove(connectionId);
  }
  
  /// Get connection for user
  static WebSocketConnection? getConnectionForUser(String userId) {
    final connectionId = _userConnections[userId];
    if (connectionId != null) {
      return _connections[connectionId];
    }
    return null;
  }
  
  /// Send message to specific connection
  static bool sendToConnection(String connectionId, WebSocketMessage message) {
    final connection = _connections[connectionId];
    if (connection != null) {
      try {
        final messageJson = jsonEncode(message.toJson());
        connection.channel.sink.add(messageJson);
        connection.lastActivity = DateTime.now();
        return true;
      } catch (e) {
        // Connection might be closed, remove it
        removeConnection(connectionId);
      }
    }
    return false;
  }
  
  /// Send message to specific user
  static bool sendToUser(String userId, WebSocketMessage message) {
    final connectionId = _userConnections[userId];
    if (connectionId != null) {
      return sendToConnection(connectionId, message);
    }
    return false;
  }
  
  /// Broadcast message to all connections
  static void broadcast(WebSocketMessage message) {
    final messageJson = jsonEncode(message.toJson());
    final connectionsToRemove = <String>[];
    
    for (final entry in _connections.entries) {
      try {
        entry.value.channel.sink.add(messageJson);
        entry.value.lastActivity = DateTime.now();
      } catch (e) {
        // Connection might be closed, mark for removal
        connectionsToRemove.add(entry.key);
      }
    }
    
    // Remove failed connections
    for (final connectionId in connectionsToRemove) {
      removeConnection(connectionId);
    }
  }
  
  /// Broadcast message to channel subscribers
  static void broadcastToChannel(String channel, WebSocketMessage message) {
    final connectionIds = _channelSubscriptions[channel] ?? <String>{};
    final messageJson = jsonEncode(message.toJson());
    final connectionsToRemove = <String>[];
    
    for (final connectionId in connectionIds) {
      final connection = _connections[connectionId];
      if (connection != null) {
        try {
          connection.channel.sink.add(messageJson);
          connection.lastActivity = DateTime.now();
        } catch (e) {
          // Connection might be closed, mark for removal
          connectionsToRemove.add(connectionId);
        }
      }
    }
    
    // Remove failed connections
    for (final connectionId in connectionsToRemove) {
      removeConnection(connectionId);
    }
  }
  
  /// Get connection statistics
  static Map<String, dynamic> getStats() {
    return {
      'totalConnections': _connections.length,
      'authenticatedConnections': _userConnections.length,
      'channels': _channelSubscriptions.keys.toList(),
      'channelSubscriptions': _channelSubscriptions.map((channel, connectionIds) => 
          MapEntry(channel, connectionIds.length)),
    };
  }
  
  /// Clean up inactive connections
  static void cleanupInactiveConnections({Duration timeout = const Duration(minutes: 30)}) {
    final now = DateTime.now();
    final connectionsToRemove = <String>[];
    
    for (final entry in _connections.entries) {
      if (now.difference(entry.value.lastActivity).compareTo(timeout) > 0) {
        connectionsToRemove.add(entry.key);
      }
    }
    
    for (final connectionId in connectionsToRemove) {
      removeConnection(connectionId);
    }
  }
}

/// WebSocket connection model
class WebSocketConnection {
  final String id;
  final WebSocketChannel channel;
  final String? userId;
  final String? sessionId;
  final DateTime connectedAt;
  DateTime lastActivity;
  final Set<String> subscribedChannels = <String>{};

  WebSocketConnection({
    required this.id,
    required this.channel,
    this.userId,
    this.sessionId,
    required this.connectedAt,
    required this.lastActivity,
  });
}

/// Enhanced WebSocket handler with proper message routing
class EnhancedWebSocketHandler {
  final ServerAuthService _authService;
  final ServerNotificationService _notificationService;
  final ServerDashboardService _dashboardService;
  final ServerLeaderboardService _leaderboardService;
  
  // Heartbeat management
  final Map<String, Timer> _heartbeatTimers = {};
  static const Duration _heartbeatInterval = Duration(seconds: 30);
  static const Duration _heartbeatTimeout = Duration(seconds: 60);

  EnhancedWebSocketHandler({
    required ServerAuthService authService,
    required ServerNotificationService notificationService,
    required ServerDashboardService dashboardService,
    required ServerLeaderboardService leaderboardService,
  })  : _authService = authService,
        _notificationService = notificationService,
        _dashboardService = dashboardService,
        _leaderboardService = leaderboardService;

  /// Handle new WebSocket connection
  void handleConnection(WebSocketChannel webSocket, {String? path}) {
    final connectionId = IdGenerator.generateShortId();
    
    // Add connection to manager
    EnhancedWebSocketManager.addConnection(connectionId, webSocket);
    
    // Send welcome message
    final welcomeMessage = WebSocketMessage(
      id: IdGenerator.generateShortId(),
      type: WebSocketMessageType.systemMessage,
      data: {
        'type': 'welcome',
        'message': 'Connected to Quester WebSocket server',
        'connectionId': connectionId,
        'timestamp': DateTime.now().toIso8601String(),
      },
      timestamp: DateTime.now(),
    );
    
    EnhancedWebSocketManager.sendToConnection(connectionId, welcomeMessage);
    
    // Start heartbeat
    _startHeartbeat(connectionId);
    
    // Listen to messages
    webSocket.stream.listen(
      (message) => _handleMessage(connectionId, message),
      onDone: () => _handleDisconnection(connectionId),
      onError: (error) => _handleError(connectionId, error),
    );
  }

  /// Handle incoming WebSocket message
  void _handleMessage(String connectionId, dynamic rawMessage) {
    try {
      final messageData = jsonDecode(rawMessage as String) as Map<String, dynamic>;
      final message = WebSocketMessage.fromJson(messageData);
      
      // Update last activity
      final connection = EnhancedWebSocketManager._connections[connectionId];
      if (connection != null) {
        connection.lastActivity = DateTime.now();
      }
      
      // Route message based on type
      switch (message.type) {
        case WebSocketMessageType.authentication:
          _handleAuthentication(connectionId, message);
          break;
          
        case WebSocketMessageType.subscribe:
          _handleSubscribe(connectionId, message);
          break;
          
        case WebSocketMessageType.unsubscribe:
          _handleUnsubscribe(connectionId, message);
          break;
          
        case WebSocketMessageType.ping:
          _handlePing(connectionId, message);
          break;
          
        case WebSocketMessageType.heartbeat:
          _handleHeartbeat(connectionId, message);
          break;
          
        default:
          _handleGenericMessage(connectionId, message);
      }
    } catch (e) {
      _sendError(connectionId, 'Invalid message format: $e');
    }
  }

  /// Handle authentication message
  void _handleAuthentication(String connectionId, WebSocketMessage message) async {
    try {
      final token = message.data['token'] as String?;
      if (token == null) {
        _sendError(connectionId, 'Authentication token required');
        return;
      }
      
      // Verify token (simplified - would use proper JWT verification)
      // For now, we'll use a simple token validation approach
      String? userId;
      try {
        // Extract user ID from token (simplified approach)
        // In a real implementation, this would properly validate the JWT
        final parts = token.split('.');
        if (parts.length == 3) {
          final payload = parts[1];
          final decoded = base64Url.decode(base64Url.normalize(payload));
          final payloadMap = jsonDecode(utf8.decode(decoded)) as Map<String, dynamic>;
          userId = payloadMap['sub'] as String?;
        }
      } catch (e) {
        userId = null;
      }
      
      if (userId == null) {
        _sendError(connectionId, 'Invalid authentication token');
        return;
      }
      
      // Update connection with user info
      final connection = EnhancedWebSocketManager._connections[connectionId];
      if (connection != null) {
        EnhancedWebSocketManager.removeConnection(connectionId);
        EnhancedWebSocketManager.addConnection(
          connectionId, 
          connection.channel, 
          userId: userId,
          sessionId: message.sessionId,
        );
      }
      
      // Send authentication success
      final response = WebSocketMessage(
        id: IdGenerator.generateShortId(),
        type: WebSocketMessageType.systemMessage,
        data: {
          'type': 'auth_success',
          'message': 'Authentication successful',
          'userId': userId,
        },
        timestamp: DateTime.now(),
        userId: userId,
      );
      
      EnhancedWebSocketManager.sendToConnection(connectionId, response);
      
    } catch (e) {
      _sendError(connectionId, 'Authentication failed: $e');
    }
  }

  /// Handle channel subscription
  void _handleSubscribe(String connectionId, WebSocketMessage message) {
    final channel = message.data['channel'] as String?;
    if (channel == null) {
      _sendError(connectionId, 'Channel name required for subscription');
      return;
    }
    
    EnhancedWebSocketManager.subscribeToChannel(connectionId, channel);
    
    final response = WebSocketMessage(
      id: IdGenerator.generateShortId(),
      type: WebSocketMessageType.systemMessage,
      data: {
        'type': 'subscribed',
        'channel': channel,
        'message': 'Successfully subscribed to $channel',
      },
      timestamp: DateTime.now(),
    );
    
    EnhancedWebSocketManager.sendToConnection(connectionId, response);
  }

  /// Handle channel unsubscription
  void _handleUnsubscribe(String connectionId, WebSocketMessage message) {
    final channel = message.data['channel'] as String?;
    if (channel == null) {
      _sendError(connectionId, 'Channel name required for unsubscription');
      return;
    }
    
    EnhancedWebSocketManager.unsubscribeFromChannel(connectionId, channel);
    
    final response = WebSocketMessage(
      id: IdGenerator.generateShortId(),
      type: WebSocketMessageType.systemMessage,
      data: {
        'type': 'unsubscribed',
        'channel': channel,
        'message': 'Successfully unsubscribed from $channel',
      },
      timestamp: DateTime.now(),
    );
    
    EnhancedWebSocketManager.sendToConnection(connectionId, response);
  }

  /// Handle ping message
  void _handlePing(String connectionId, WebSocketMessage message) {
    final pongMessage = WebSocketMessage.pong(
      id: IdGenerator.generateShortId(),
      sessionId: message.sessionId,
    );
    
    EnhancedWebSocketManager.sendToConnection(connectionId, pongMessage);
  }

  /// Handle heartbeat message
  void _handleHeartbeat(String connectionId, WebSocketMessage message) {
    // Reset heartbeat timer
    _resetHeartbeat(connectionId);
  }

  /// Handle generic message
  void _handleGenericMessage(String connectionId, WebSocketMessage message) {
    // Log unhandled message types
    print('Unhandled WebSocket message type: ${message.type}');
  }

  /// Handle connection disconnection
  void _handleDisconnection(String connectionId) {
    EnhancedWebSocketManager.removeConnection(connectionId);
    _stopHeartbeat(connectionId);
    print('WebSocket connection closed: $connectionId');
  }

  /// Handle connection error
  void _handleError(String connectionId, dynamic error) {
    print('WebSocket error for connection $connectionId: $error');
    _handleDisconnection(connectionId);
  }

  /// Send error message to connection
  void _sendError(String connectionId, String errorMessage) {
    final errorMsg = WebSocketMessage(
      id: IdGenerator.generateShortId(),
      type: WebSocketMessageType.error,
      data: {
        'error': errorMessage,
        'timestamp': DateTime.now().toIso8601String(),
      },
      timestamp: DateTime.now(),
    );
    
    EnhancedWebSocketManager.sendToConnection(connectionId, errorMsg);
  }

  /// Start heartbeat for connection
  void _startHeartbeat(String connectionId) {
    _heartbeatTimers[connectionId] = Timer.periodic(_heartbeatInterval, (timer) {
      final connection = EnhancedWebSocketManager._connections[connectionId];
      if (connection == null) {
        timer.cancel();
        _heartbeatTimers.remove(connectionId);
        return;
      }
      
      // Check if connection is still active
      final now = DateTime.now();
      if (now.difference(connection.lastActivity).compareTo(_heartbeatTimeout) > 0) {
        // Connection is inactive, close it
        timer.cancel();
        _handleDisconnection(connectionId);
        return;
      }
      
      // Send heartbeat
      final heartbeatMessage = WebSocketMessage(
        id: IdGenerator.generateShortId(),
        type: WebSocketMessageType.heartbeat,
        data: {'timestamp': now.toIso8601String()},
        timestamp: now,
      );
      
      EnhancedWebSocketManager.sendToConnection(connectionId, heartbeatMessage);
    });
  }

  /// Reset heartbeat timer for connection
  void _resetHeartbeat(String connectionId) {
    _stopHeartbeat(connectionId);
    _startHeartbeat(connectionId);
  }

  /// Stop heartbeat for connection
  void _stopHeartbeat(String connectionId) {
    _heartbeatTimers[connectionId]?.cancel();
    _heartbeatTimers.remove(connectionId);
  }

  /// Get WebSocket statistics
  Map<String, dynamic> getStats() {
    return EnhancedWebSocketManager.getStats();
  }

  /// Cleanup inactive connections
  void cleanupConnections() {
    EnhancedWebSocketManager.cleanupInactiveConnections();
  }
}

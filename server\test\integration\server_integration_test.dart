import 'package:test/test.dart';
import 'package:shared/shared.dart';
import '../../lib/services/auth_service_impl.dart';
import '../../lib/services/user_repository_impl.dart';
import '../../lib/services/quest_repository_impl.dart';
import '../../lib/services/notification_service_impl.dart';
import '../../lib/services/notification_repository_impl.dart';
import '../../lib/services/dashboard_service_impl.dart';
import '../../lib/services/dashboard_repository_impl.dart';
import '../../lib/services/achievement_service_impl.dart';
import '../../lib/services/achievement_repository_impl.dart';
import '../../lib/services/leaderboard_service_impl.dart';
import '../../lib/services/leaderboard_repository_impl.dart';
import '../../lib/services/error_handler_impl.dart';
import '../test_utils.dart';

void main() {
  group('Server Integration Tests', () {
    late ServerAuthService authService;
    late InMemoryUserRepository userRepository;
    late InMemoryQuestRepository questRepository;
    late ServerNotificationService notificationService;
    late InMemoryNotificationRepository notificationRepository;
    late ServerDashboardService dashboardService;
    late InMemoryDashboardRepository dashboardRepository;
    late ServerAchievementService achievementService;
    late InMemoryAchievementRepository achievementRepository;
    late ServerLeaderboardService leaderboardService;
    late InMemoryLeaderboardRepository leaderboardRepository;
    late ServerErrorHandler errorHandler;

    setUp(() {
      // Initialize repositories
      userRepository = InMemoryUserRepository();
      questRepository = InMemoryQuestRepository();
      notificationRepository = InMemoryNotificationRepository();
      dashboardRepository = InMemoryDashboardRepository(
        userRepository: userRepository,
        questRepository: questRepository,
        notificationRepository: notificationRepository,
      );
      achievementRepository = InMemoryAchievementRepository();
      leaderboardRepository = InMemoryLeaderboardRepository(
        userRepository: userRepository,
        questRepository: questRepository,
        achievementRepository: achievementRepository,
      );

      // Initialize services
      final tokenManager = EnhancedTokenManager();
      authService = ServerAuthService(userRepository, tokenManager);
      notificationService = ServerNotificationService(notificationRepository);
      dashboardService = ServerDashboardService(
        dashboardRepository: dashboardRepository,
        userRepository: userRepository,
        questRepository: questRepository,
        notificationService: notificationService,
      );
      achievementService = ServerAchievementService(
        achievementRepository: achievementRepository,
        userRepository: userRepository,
        questRepository: questRepository,
        notificationService: notificationService,
      );
      leaderboardService = ServerLeaderboardService(
        leaderboardRepository: leaderboardRepository,
        userRepository: userRepository,
        notificationService: notificationService,
      );
      errorHandler = ServerErrorHandler.instance;
      errorHandler.clearErrorLogs();
    });

    tearDown(() {
      // Clean up if needed
    });

    group('Complete User Workflow', () {
      test('should handle complete user registration and login flow', () async {
        // Step 1: Register a new user
        final registerResult = await authService.register(
          username: 'testuser',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          firstName: 'Test',
          lastName: 'User',
        );

        expect(registerResult.success, isTrue);
        expect(registerResult.user, isNotNull);

        final userId = registerResult.user!.id;

        // Step 2: Login with the registered user
        final loginResult = await authService.login(
          email: '<EMAIL>',
          password: 'TestPassword123!',
        );

        expect(loginResult.success, isTrue);
        expect(loginResult.token, isNotNull);
        expect(loginResult.refreshToken, isNotNull);

        // Step 3: Validate the access token
        final validateResult = await authService.verifyAuth();
        expect(validateResult, isTrue);

        final currentUser = await authService.getCurrentUser();
        expect(currentUser, isNotNull);
        expect(currentUser!.id, equals(userId));

        // Step 4: Check user exists in repository
        final userExists = await userRepository.exists(userId);
        expect(userExists, isTrue);

        // Step 5: Get user from repository
        final retrievedUser = await userRepository.getById(userId);
        expect(retrievedUser, isNotNull);
        expect(retrievedUser!.username, equals('testuser'));
        expect(retrievedUser.email, equals('<EMAIL>'));
      });

      test('should handle user quest workflow', () async {
        // Step 1: Create a user
        final user = TestUtils.createTestUser();
        await userRepository.create(user);

        // Step 2: Create a quest
        final quest = TestUtils.createTestQuest();
        await questRepository.create(quest);

        // Step 3: Assign quest to user
        await questRepository.assignToUser(quest.id, user.id);

        // Step 4: Get user's assigned quests
        final assignedQuests = await questRepository.getAssignedToUser(user.id);
        expect(assignedQuests.length, equals(1));
        expect(assignedQuests.first.id, equals(quest.id));

        // Step 5: Complete the quest
        final completedQuest = await questRepository.completeQuest(
          quest.id,
          user.id,
          {'completionTime': DateTime.now().toIso8601String()},
        );

        expect(completedQuest.status, equals(QuestStatus.completed));

        // Step 6: Verify quest completion in repository
        final retrievedQuest = await questRepository.getById(quest.id);
        expect(retrievedQuest!.status, equals(QuestStatus.completed));
      });

      test('should handle notification workflow', () async {
        // Step 1: Create a user
        final user = TestUtils.createTestUser();
        await userRepository.create(user);

        // Step 2: Send notification to user
        await notificationService.sendToUser(
          userId: user.id,
          title: 'Test Notification',
          message: 'This is a test notification',
        );

        // Step 3: Get user notifications
        final userNotifications = await notificationService.getNotificationsForUser(user.id);
        expect(userNotifications.length, equals(1));
        expect(userNotifications.first.userId, equals(user.id));

        // Step 4: Get unread count
        final unreadCount = await notificationService.getUnreadCountForUser(user.id);
        expect(unreadCount, equals(1));

        // Step 5: Mark notification as read
        await notificationService.markAsRead(userNotifications.first.id);

        // Step 6: Verify unread count decreased
        final newUnreadCount = await notificationService.getUnreadCountForUser(user.id);
        expect(newUnreadCount, equals(0));
      });

      test('should handle achievement workflow', () async {
        // Step 1: Create a user
        final user = TestUtils.createTestUser();
        await userRepository.create(user);

        // Step 2: Create an achievement
        final achievement = TestUtils.createTestAchievement();
        await achievementRepository.create(achievement);

        // Step 3: Award achievement to user (using repository directly for test)
        await achievementRepository.awardToUser(achievement.id, user.id);

        // Step 4: Get user achievements
        final userAchievements = await achievementService.getUserAchievements(user.id);
        expect(userAchievements.length, equals(1));
        expect(userAchievements.first.id, equals(achievement.id));

        // Step 5: Check if user has achievement
        final hasAchievement = await achievementRepository.userHasAchievement(user.id, achievement.id);
        expect(hasAchievement, isTrue);

        // Step 6: Get achievement progress
        final progress = await achievementRepository.getProgressForUser(user.id, achievement.id);
        expect(progress['progress'], equals(1.0)); // 100% complete
      });

      test('should handle leaderboard workflow', () async {
        // Step 1: Create multiple users
        final users = TestUtils.createTestUsers(5);
        for (final user in users) {
          await userRepository.create(user);
        }

        // Step 2: Update user scores to populate leaderboard
        for (int i = 0; i < users.length; i++) {
          await leaderboardRepository.updateUserScore('global', users[i].id, 1000 - (i * 100));
        }

        // Step 3: Get leaderboard
        final leaderboard = await leaderboardService.getLeaderboardById('global');
        expect(leaderboard, isNotNull);

        // Step 4: Get user position
        final userPosition = await leaderboardService.getUserPosition('global', users[2].id);
        expect(userPosition, isNotNull);

        // Step 5: Update user score
        await leaderboardRepository.updateUserScore('global', users[4].id, 1500);

        // Step 6: Get updated leaderboard
        final updatedLeaderboard = await leaderboardService.getLeaderboardById('global');
        expect(updatedLeaderboard, isNotNull);
      });
    });

    group('Dashboard Integration', () {
      test('should generate comprehensive dashboard stats', () async {
        // Step 1: Create test data
        final users = TestUtils.createTestUsers(10);
        for (final user in users) {
          await userRepository.create(user);
        }

        final quests = TestUtils.createTestQuests(15, createdBy: users.first.id);
        for (final quest in quests) {
          await questRepository.create(quest);
        }

        final notifications = TestUtils.createTestNotifications(25, userId: users.first.id);
        for (final notification in notifications) {
          await notificationRepository.create(notification);
        }

        // Step 2: Get dashboard stats
        final stats = await dashboardService.getStats();

        // Step 3: Verify stats
        expect(stats.totalUsers, equals(10));
        expect(stats.totalQuests, greaterThanOrEqualTo(0));
        expect(stats.lastUpdated, isNotNull);

        // Step 4: Get user dashboard
        final userDashboard = await dashboardService.getUserDashboard(users.first.id);
        expect(userDashboard, isNotNull);
        expect(userDashboard['userId'], equals(users.first.id));

        // Step 5: Get recent activity
        final recentActivity = await dashboardService.getRecentActivity();
        expect(recentActivity, isNotNull);
      });
    });

    group('Error Handling Integration', () {
      test('should handle errors across all services', () async {
        // Test auth service error handling
        final authResult = await authService.login(
          email: '<EMAIL>',
          password: 'wrongpassword',
        );
        expect(authResult.success, isFalse);

        // Test repository error handling
        expect(
          () => userRepository.update('non-existent-id', TestUtils.createTestUser()),
          throwsException,
        );

        // Test notification service error handling
        final notificationResult = await notificationService.getNotificationsForUser('non-existent-user');
        expect(notificationResult.isEmpty, isTrue);

        // Test achievement service error handling
        final achievementResult = await achievementService.getUserAchievements('non-existent-user');
        expect(achievementResult.isEmpty, isTrue);

        // Test leaderboard service error handling
        final leaderboardResult = await leaderboardService.getLeaderboardById('non-existent-board');
        expect(leaderboardResult, isNull);
      });
    });

    group('Performance and Scalability', () {
      test('should handle large datasets efficiently', () async {
        // Create large dataset
        final users = TestUtils.createTestUsers(100);
        final quests = TestUtils.createTestQuests(200);
        final notifications = TestUtils.createTestNotifications(500);

        // Measure creation time
        final stopwatch = Stopwatch()..start();

        for (final user in users) {
          await userRepository.create(user);
        }

        for (final quest in quests) {
          await questRepository.create(quest);
        }

        for (final notification in notifications) {
          await notificationRepository.create(notification);
        }

        stopwatch.stop();

        // Verify data was created
        expect(await userRepository.count(), equals(100));
        expect(await questRepository.count(), equals(200));
        expect(await notificationRepository.count(), equals(500));

        // Performance should be reasonable (less than 5 seconds for in-memory operations)
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
      });

      test('should handle concurrent operations', () async {
        // Create concurrent user registrations
        final futures = <Future<AuthResult>>[];
        
        for (int i = 0; i < 10; i++) {
          futures.add(authService.register(
            username: 'user$i',
            email: 'user$<EMAIL>',
            password: 'Password123!',
            firstName: 'User',
            lastName: '$i',
          ));
        }

        // Wait for all registrations to complete
        final results = await Future.wait(futures);

        // Verify all registrations succeeded
        for (final result in results) {
          expect(result.success, isTrue);
        }

        // Verify all users were created
        expect(await userRepository.count(), equals(10));
      });
    });
  });
}

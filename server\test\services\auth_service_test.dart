import 'package:test/test.dart';
import 'package:shared/shared.dart';
import '../../lib/services/auth_service_impl.dart';
import '../../lib/services/user_repository_impl.dart';
import '../test_utils.dart';

void main() {
  group('ServerAuthService Tests', () {
    late ServerAuthService authService;
    late InMemoryUserRepository userRepository;

    setUp(() {
      userRepository = InMemoryUserRepository();
      authService = ServerAuthService(userRepository: userRepository);
    });

    tearDown(() {
      authService.dispose();
    });

    group('User Registration', () {
      test('should register new user successfully', () async {
        // Arrange
        const username = 'testuser';
        const email = '<EMAIL>';
        const password = 'TestPassword123!';
        const firstName = 'Test';
        const lastName = 'User';

        // Act
        final result = await authService.register(
          username: username,
          email: email,
          password: password,
          firstName: firstName,
          lastName: lastName,
        );

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.user, isNotNull);
        expect(result.user!.username, equals(username));
        expect(result.user!.email, equals(email));
        expect(result.user!.firstName, equals(firstName));
        expect(result.user!.lastName, equals(lastName));
        expect(result.user!.role, equals(UserRole.user));
        expect(result.user!.isActive, isTrue);
      });

      test('should fail registration with duplicate email', () async {
        // Arrange
        const email = '<EMAIL>';
        await authService.register(
          username: 'user1',
          email: email,
          password: 'Password123!',
          firstName: 'User',
          lastName: 'One',
        );

        // Act
        final result = await authService.register(
          username: 'user2',
          email: email,
          password: 'Password123!',
          firstName: 'User',
          lastName: 'Two',
        );

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.message, contains('already exists'));
      });

      test('should fail registration with duplicate username', () async {
        // Arrange
        const username = 'testuser';
        await authService.register(
          username: username,
          email: '<EMAIL>',
          password: 'Password123!',
          firstName: 'User',
          lastName: 'One',
        );

        // Act
        final result = await authService.register(
          username: username,
          email: '<EMAIL>',
          password: 'Password123!',
          firstName: 'User',
          lastName: 'Two',
        );

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.message, contains('already taken'));
      });

      test('should fail registration with invalid email', () async {
        // Act
        final result = await authService.register(
          username: 'testuser',
          email: 'invalid-email',
          password: 'Password123!',
          firstName: 'Test',
          lastName: 'User',
        );

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.message, contains('Invalid email'));
      });

      test('should fail registration with weak password', () async {
        // Act
        final result = await authService.register(
          username: 'testuser',
          email: '<EMAIL>',
          password: '123',
          firstName: 'Test',
          lastName: 'User',
        );

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.message, contains('Password'));
      });
    });

    group('User Login', () {
      test('should login with valid credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'TestPassword123!';
        
        await authService.register(
          username: 'testuser',
          email: email,
          password: password,
          firstName: 'Test',
          lastName: 'User',
        );

        // Act
        final result = await authService.login(
          email: email,
          password: password,
        );

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.user, isNotNull);
        expect(result.user!.email, equals(email));
        expect(result.accessToken, isNotNull);
        expect(result.refreshToken, isNotNull);
      });

      test('should fail login with invalid email', () async {
        // Act
        final result = await authService.login(
          email: '<EMAIL>',
          password: 'Password123!',
        );

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.message, contains('Invalid'));
      });

      test('should fail login with invalid password', () async {
        // Arrange
        const email = '<EMAIL>';
        
        await authService.register(
          username: 'testuser',
          email: email,
          password: 'TestPassword123!',
          firstName: 'Test',
          lastName: 'User',
        );

        // Act
        final result = await authService.login(
          email: email,
          password: 'WrongPassword',
        );

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.message, contains('Invalid'));
      });

      test('should fail login with inactive user', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'TestPassword123!';
        
        final registerResult = await authService.register(
          username: 'testuser',
          email: email,
          password: password,
          firstName: 'Test',
          lastName: 'User',
        );

        // Deactivate user
        final user = registerResult.user!.copyWith(isActive: false);
        await userRepository.update(user.id, user);

        // Act
        final result = await authService.login(
          email: email,
          password: password,
        );

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.message, contains('inactive'));
      });
    });

    group('Token Management', () {
      test('should refresh access token with valid refresh token', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'TestPassword123!';
        
        await authService.register(
          username: 'testuser',
          email: email,
          password: password,
          firstName: 'Test',
          lastName: 'User',
        );

        final loginResult = await authService.login(
          email: email,
          password: password,
        );

        // Act
        final result = await authService.refreshToken(
          refreshToken: loginResult.refreshToken!,
        );

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.accessToken, isNotNull);
        expect(result.refreshToken, isNotNull);
        expect(result.accessToken, isNot(equals(loginResult.accessToken)));
      });

      test('should fail refresh with invalid token', () async {
        // Act
        final result = await authService.refreshToken(
          refreshToken: 'invalid-token',
        );

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.message, contains('Invalid'));
      });

      test('should validate valid access token', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'TestPassword123!';
        
        await authService.register(
          username: 'testuser',
          email: email,
          password: password,
          firstName: 'Test',
          lastName: 'User',
        );

        final loginResult = await authService.login(
          email: email,
          password: password,
        );

        // Act
        final result = await authService.validateToken(
          token: loginResult.accessToken!,
        );

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.user, isNotNull);
        expect(result.user!.email, equals(email));
      });

      test('should fail validation with invalid token', () async {
        // Act
        final result = await authService.validateToken(
          token: 'invalid-token',
        );

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.message, contains('Invalid'));
      });
    });

    group('User Logout', () {
      test('should logout user successfully', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'TestPassword123!';
        
        await authService.register(
          username: 'testuser',
          email: email,
          password: password,
          firstName: 'Test',
          lastName: 'User',
        );

        final loginResult = await authService.login(
          email: email,
          password: password,
        );

        // Act
        final result = await authService.logout(
          refreshToken: loginResult.refreshToken!,
        );

        // Assert
        expect(result.isSuccess, isTrue);
        
        // Verify token is invalidated
        final validateResult = await authService.validateToken(
          token: loginResult.accessToken!,
        );
        expect(validateResult.isSuccess, isFalse);
      });
    });

    group('Auth State Management', () {
      test('should track authentication state changes', () async {
        // Arrange
        final stateChanges = <AuthState>[];
        authService.authStateStream.listen((state) {
          stateChanges.add(state);
        });

        // Act
        await authService.register(
          username: 'testuser',
          email: '<EMAIL>',
          password: 'TestPassword123!',
          firstName: 'Test',
          lastName: 'User',
        );

        await TestUtils.waitForAsync();

        // Assert
        expect(stateChanges, contains(AuthState.authenticated));
      });

      test('should get current auth state', () {
        // Act
        final state = authService.currentState;

        // Assert
        expect(state, equals(AuthState.unauthenticated));
      });
    });


  });
}

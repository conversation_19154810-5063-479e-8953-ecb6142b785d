import 'package:test/test.dart';
import 'package:shared/shared.dart';
import '../../lib/services/error_handler_impl.dart';
import '../test_utils.dart';

void main() {
  group('ServerErrorHandler Tests', () {
    late ServerErrorHandler errorHandler;

    setUp(() {
      errorHandler = ServerErrorHandler.instance;
      errorHandler.clearErrorLogs(); // Clear any previous logs
    });

    tearDown(() {
      errorHandler.dispose();
    });

    group('Error Handling', () {
      test('should handle validation exception correctly', () async {
        // Arrange
        final validationException = TestUtils.createTestValidationException();
        final context = ErrorContext(timestamp: DateTime.now());

        // Act
        final result = await errorHandler.handleError<String>(
          validationException,
          context: context,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.message, equals(validationException.message));
        expect(result.statusCode, equals(400));
        expect(result.errors, isNotNull);
        expect(result.errors!['code'], equals(validationException.code));
      });

      test('should handle auth exception correctly', () async {
        // Arrange
        final authException = TestUtils.createTestAuthException();
        final context = ErrorContext(timestamp: DateTime.now());

        // Act
        final result = await errorHandler.handleError<String>(
          authException,
          context: context,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.message, equals(authException.message));
        expect(result.statusCode, equals(401));
        expect(result.errors, isNotNull);
        expect(result.errors!['code'], equals(authException.code));
      });

      test('should handle quest exception correctly', () async {
        // Arrange
        final questException = TestUtils.createTestQuestException();
        final context = ErrorContext(timestamp: DateTime.now());

        // Act
        final result = await errorHandler.handleError<String>(
          questException,
          context: context,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.message, equals(questException.message));
        expect(result.statusCode, equals(400));
        expect(result.errors, isNotNull);
        expect(result.errors!['code'], equals(questException.code));
      });

      test('should handle API exception correctly', () async {
        // Arrange
        final apiException = TestUtils.createTestApiException(statusCode: 500);
        final context = ErrorContext(timestamp: DateTime.now());

        // Act
        final result = await errorHandler.handleError<String>(
          apiException,
          context: context,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.message, equals(apiException.message));
        expect(result.statusCode, equals(500));
        expect(result.errors, isNotNull);
        expect(result.errors!['code'], equals(apiException.code));
      });

      test('should handle generic exception correctly', () async {
        // Arrange
        final exception = Exception('Generic test error');
        final context = ErrorContext(timestamp: DateTime.now());

        // Act
        final result = await errorHandler.handleError<String>(
          exception,
          context: context,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.message, contains('Generic test error'));
        expect(result.statusCode, equals(500));
        expect(result.errors, isNotNull);
      });

      test('should handle unknown error correctly', () async {
        // Arrange
        const error = 'String error message';
        final context = ErrorContext(timestamp: DateTime.now());

        // Act
        final result = await errorHandler.handleError<String>(
          error,
          context: context,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.message, equals(error));
        expect(result.statusCode, equals(500));
        expect(result.errors, isNotNull);
      });

      test('should use custom message when provided', () async {
        // Arrange
        final exception = Exception('Original message');
        const customMessage = 'Custom error message';
        final context = ErrorContext(timestamp: DateTime.now());

        // Act
        final result = await errorHandler.handleError<String>(
          exception,
          context: context,
          customMessage: customMessage,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.message, equals(customMessage));
      });

      test('should use custom severity when provided', () async {
        // Arrange
        final exception = Exception('Test error');
        final context = ErrorContext(timestamp: DateTime.now());

        // Act
        await errorHandler.handleError<String>(
          exception,
          context: context,
          severity: ErrorSeverity.critical,
        );

        // Assert
        final logs = errorHandler.getErrorLogs(limit: 1);
        expect(logs.first.severity, equals(ErrorSeverity.critical));
      });
    });

    group('Specific Error Handlers', () {
      test('should handle validation error correctly', () async {
        // Arrange
        final validationException = TestUtils.createTestValidationException(
          fieldErrors: {'email': 'Invalid email', 'password': 'Too weak'},
        );
        final context = ErrorContext(timestamp: DateTime.now());

        // Act
        final result = await errorHandler.handleValidationError<String>(
          validationException,
          context: context,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.statusCode, equals(400));
        expect(result.errors!['fieldErrors'], equals(validationException.fieldErrors));
      });

      test('should handle auth error correctly', () async {
        // Arrange
        const message = 'Authentication failed';
        final context = ErrorContext(timestamp: DateTime.now());

        // Act
        final result = await errorHandler.handleAuthError<String>(
          message,
          context: context,
          errorCode: 'INVALID_TOKEN',
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.message, equals(message));
        expect(result.statusCode, equals(401));
        expect(result.errors!['code'], equals('INVALID_TOKEN'));
      });

      test('should handle authorization error correctly', () async {
        // Arrange
        const message = 'Access denied';
        final context = ErrorContext(timestamp: DateTime.now());

        // Act
        final result = await errorHandler.handleAuthorizationError<String>(
          message,
          context: context,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.message, equals(message));
        expect(result.statusCode, equals(403));
        expect(result.errors!['code'], equals('FORBIDDEN'));
      });

      test('should handle not found error correctly', () async {
        // Arrange
        const resource = 'User';
        final context = ErrorContext(timestamp: DateTime.now());

        // Act
        final result = await errorHandler.handleNotFoundError<String>(
          resource,
          context: context,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.message, equals('User not found'));
        expect(result.statusCode, equals(404));
        expect(result.errors!['code'], equals('NOT_FOUND'));
        expect(result.errors!['resource'], equals(resource));
      });

      test('should handle rate limit error correctly', () async {
        // Arrange
        final context = ErrorContext(timestamp: DateTime.now());
        const retryAfter = 60;

        // Act
        final result = await errorHandler.handleRateLimitError<String>(
          context: context,
          retryAfter: retryAfter,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.message, equals('Rate limit exceeded'));
        expect(result.statusCode, equals(429));
        expect(result.errors!['code'], equals('RATE_LIMIT_EXCEEDED'));
        expect(result.errors!['retryAfter'], equals(retryAfter));
      });
    });

    group('Error Logging', () {
      test('should log errors correctly', () async {
        // Arrange
        final exception = Exception('Test error');
        final context = ErrorContext(
          userId: 'user123',
          endpoint: '/api/test',
          method: 'POST',
          timestamp: DateTime.now(),
        );

        // Act
        await errorHandler.handleError<String>(
          exception,
          context: context,
        );

        // Assert
        final logs = errorHandler.getErrorLogs();
        expect(logs.length, equals(1));

        final log = logs.first;
        expect(log.message, contains('Test error'));
        expect(log.errorType, equals('Exception'));
        expect(log.statusCode, equals(500));
        expect(log.context.userId, equals('user123'));
        expect(log.context.endpoint, equals('/api/test'));
        expect(log.context.method, equals('POST'));
      });

      test('should filter error logs by severity', () async {
        // Arrange
        await errorHandler.handleError<String>(
          Exception('Low severity'),
          severity: ErrorSeverity.low,
        );
        await errorHandler.handleError<String>(
          Exception('High severity'),
          severity: ErrorSeverity.high,
        );
        await errorHandler.handleError<String>(
          Exception('Critical severity'),
          severity: ErrorSeverity.critical,
        );

        // Act
        final highSeverityLogs = errorHandler.getErrorLogs(severity: ErrorSeverity.high);
        final criticalSeverityLogs = errorHandler.getErrorLogs(severity: ErrorSeverity.critical);

        // Assert
        expect(highSeverityLogs.length, equals(1));
        expect(criticalSeverityLogs.length, equals(1));
        expect(highSeverityLogs.first.message, contains('High severity'));
        expect(criticalSeverityLogs.first.message, contains('Critical severity'));
      });

      test('should filter error logs by type', () async {
        // Arrange
        await errorHandler.handleError<String>(Exception('Exception error'));
        await errorHandler.handleError<String>(TestUtils.createTestValidationException());

        // Act
        final exceptionLogs = errorHandler.getErrorLogs(errorType: 'Exception');
        final validationLogs = errorHandler.getErrorLogs(errorType: 'ValidationException');

        // Assert
        expect(exceptionLogs.length, equals(1));
        expect(validationLogs.length, equals(1));
      });

      test('should filter error logs by date', () async {
        // Arrange
        final pastDate = DateTime.now().subtract(const Duration(hours: 2));
        
        await errorHandler.handleError<String>(Exception('Old error'));
        await TestUtils.waitForAsync(const Duration(milliseconds: 100));
        await errorHandler.handleError<String>(Exception('New error'));

        // Act
        final recentLogs = errorHandler.getErrorLogs(since: pastDate);

        // Assert
        expect(recentLogs.length, equals(2));
      });

      test('should limit error logs correctly', () async {
        // Arrange
        for (int i = 0; i < 5; i++) {
          await errorHandler.handleError<String>(Exception('Error $i'));
        }

        // Act
        final limitedLogs = errorHandler.getErrorLogs(limit: 3);

        // Assert
        expect(limitedLogs.length, equals(3));
      });

      test('should clear error logs', () async {
        // Arrange
        await errorHandler.handleError<String>(Exception('Test error'));
        expect(errorHandler.getErrorLogs().length, equals(1));

        // Act
        errorHandler.clearErrorLogs();

        // Assert
        expect(errorHandler.getErrorLogs().length, equals(0));
      });
    });

    group('Error Statistics', () {
      test('should generate error statistics correctly', () async {
        // Arrange
        await errorHandler.handleError<String>(
          Exception('Error 1'),
          severity: ErrorSeverity.low,
        );
        await errorHandler.handleError<String>(
          TestUtils.createTestValidationException(),
          severity: ErrorSeverity.medium,
        );
        await errorHandler.handleError<String>(
          TestUtils.createTestAuthException(),
          severity: ErrorSeverity.high,
        );

        // Act
        final stats = errorHandler.getErrorStats();

        // Assert
        expect(stats['totalErrors'], equals(3));
        expect(stats['severityBreakdown']['low'], equals(1));
        expect(stats['severityBreakdown']['medium'], equals(1));
        expect(stats['severityBreakdown']['high'], equals(1));
        expect(stats['typeBreakdown']['Exception'], equals(1));
        expect(stats['typeBreakdown']['ValidationException'], equals(1));
        expect(stats['typeBreakdown']['AuthException'], equals(1));
      });

      test('should generate statistics for specific time period', () async {
        // Arrange
        final pastDate = DateTime.now().subtract(const Duration(hours: 1));
        
        await errorHandler.handleError<String>(Exception('Old error'));
        await TestUtils.waitForAsync(const Duration(milliseconds: 100));
        await errorHandler.handleError<String>(Exception('New error'));

        // Act
        final allTimeStats = errorHandler.getErrorStats();
        final recentStats = errorHandler.getErrorStats(since: pastDate);

        // Assert
        expect(allTimeStats['totalErrors'], equals(2));
        expect(recentStats['totalErrors'], equals(2));
      });
    });
  });
}

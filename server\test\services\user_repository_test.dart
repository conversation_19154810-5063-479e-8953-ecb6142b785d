import 'package:test/test.dart';
import 'package:shared/shared.dart';
import '../../lib/services/user_repository_impl.dart';
import '../test_utils.dart';

void main() {
  group('InMemoryUserRepository Tests', () {
    late InMemoryUserRepository repository;

    setUp(() {
      repository = InMemoryUserRepository();
    });

    group('Basic CRUD Operations', () {
      test('should create user successfully', () async {
        // Arrange
        final user = TestUtils.createTestUser();

        // Act
        final result = await repository.create(user);

        // Assert
        expect(result, equals(user));
        expect(await repository.exists(user.id), isTrue);
      });

      test('should get user by id', () async {
        // Arrange
        final user = TestUtils.createTestUser();
        await repository.create(user);

        // Act
        final result = await repository.getById(user.id);

        // Assert
        expect(result, isNotNull);
        expect(result!.id, equals(user.id));
        expect(result.username, equals(user.username));
        expect(result.email, equals(user.email));
      });

      test('should return null for non-existent user', () async {
        // Act
        final result = await repository.getById('non-existent-id');

        // Assert
        expect(result, isNull);
      });

      test('should update user successfully', () async {
        // Arrange
        final user = TestUtils.createTestUser();
        await repository.create(user);

        final updatedUser = user.copyWith(
          firstName: 'Updated',
          lastName: 'Name',
        );

        // Act
        final result = await repository.update(user.id, updatedUser);

        // Assert
        expect(result.firstName, equals('Updated'));
        expect(result.lastName, equals('Name'));

        final retrieved = await repository.getById(user.id);
        expect(retrieved!.firstName, equals('Updated'));
        expect(retrieved.lastName, equals('Name'));
      });

      test('should throw exception when updating non-existent user', () async {
        // Arrange
        final user = TestUtils.createTestUser();

        // Act & Assert
        expect(
          () => repository.update('non-existent-id', user),
          throwsException,
        );
      });

      test('should delete user successfully', () async {
        // Arrange
        final user = TestUtils.createTestUser();
        await repository.create(user);

        // Act
        await repository.delete(user.id);

        // Assert
        expect(await repository.exists(user.id), isFalse);
        expect(await repository.getById(user.id), isNull);
      });

      test('should check user existence correctly', () async {
        // Arrange
        final user = TestUtils.createTestUser();

        // Act & Assert
        expect(await repository.exists(user.id), isFalse);

        await repository.create(user);
        expect(await repository.exists(user.id), isTrue);

        await repository.delete(user.id);
        expect(await repository.exists(user.id), isFalse);
      });
    });

    group('Query Operations', () {
      test('should get all users with pagination', () async {
        // Arrange
        final users = TestUtils.createTestUsers(25);
        for (final user in users) {
          await repository.create(user);
        }

        // Act
        final page1 = await repository.getAll(page: 1, pageSize: 10);
        final page2 = await repository.getAll(page: 2, pageSize: 10);
        final page3 = await repository.getAll(page: 3, pageSize: 10);

        // Assert
        expect(page1.length, equals(10));
        expect(page2.length, equals(10));
        expect(page3.length, equals(5));
      });

      test('should count users correctly', () async {
        // Arrange
        final users = TestUtils.createTestUsers(5);
        for (final user in users) {
          await repository.create(user);
        }

        // Act
        final count = await repository.count();

        // Assert
        expect(count, equals(5));
      });

      test('should search users by username and email', () async {
        // Arrange
        final user1 = TestUtils.createTestUser(username: 'john_doe', email: '<EMAIL>');
        final user2 = TestUtils.createTestUser(username: 'jane_smith', email: '<EMAIL>');
        final user3 = TestUtils.createTestUser(username: 'bob_wilson', email: '<EMAIL>');

        await repository.create(user1);
        await repository.create(user2);
        await repository.create(user3);

        // Act
        final johnResults = await repository.search('john');
        final exampleResults = await repository.search('example');
        final noResults = await repository.search('xyz');

        // Assert
        expect(johnResults.length, equals(1));
        expect(johnResults.first.username, equals('john_doe'));

        expect(exampleResults.length, equals(2));
        expect(noResults.length, equals(0));
      });

      test('should get users by email', () async {
        // Arrange
        final user = TestUtils.createTestUser(email: '<EMAIL>');
        await repository.create(user);

        // Act
        final result = await repository.getByEmail('<EMAIL>');
        final notFound = await repository.getByEmail('<EMAIL>');

        // Assert
        expect(result, isNotNull);
        expect(result!.email, equals('<EMAIL>'));
        expect(notFound, isNull);
      });

      test('should get users by username', () async {
        // Arrange
        final user = TestUtils.createTestUser(username: 'testuser');
        await repository.create(user);

        // Act
        final result = await repository.getByUsername('testuser');
        final notFound = await repository.getByUsername('notfound');

        // Assert
        expect(result, isNotNull);
        expect(result!.username, equals('testuser'));
        expect(notFound, isNull);
      });

      test('should get users by role', () async {
        // Arrange
        final admin = TestUtils.createTestUser(role: UserRole.admin);
        final moderator = TestUtils.createTestUser(role: UserRole.moderator);
        final user1 = TestUtils.createTestUser(role: UserRole.user);
        final user2 = TestUtils.createTestUser(role: UserRole.user);

        await repository.create(admin);
        await repository.create(moderator);
        await repository.create(user1);
        await repository.create(user2);

        // Act
        final admins = await repository.getByRole(UserRole.admin);
        final moderators = await repository.getByRole(UserRole.moderator);
        final users = await repository.getByRole(UserRole.user);

        // Assert
        expect(admins.length, equals(1));
        expect(moderators.length, equals(1));
        expect(users.length, equals(2));
      });

      test('should get active users only', () async {
        // Arrange
        final activeUser1 = TestUtils.createTestUser(isActive: true);
        final activeUser2 = TestUtils.createTestUser(isActive: true);
        final inactiveUser = TestUtils.createTestUser(isActive: false);

        await repository.create(activeUser1);
        await repository.create(activeUser2);
        await repository.create(inactiveUser);

        // Act
        final activeUsers = await repository.getActiveUsers();

        // Assert
        expect(activeUsers.length, equals(2));
        expect(activeUsers.every((user) => user.isActive), isTrue);
      });

      test('should filter users by criteria', () async {
        // Arrange
        final admin = TestUtils.createTestUser(role: UserRole.admin, isActive: true);
        final activeUser = TestUtils.createTestUser(role: UserRole.user, isActive: true);
        final inactiveUser = TestUtils.createTestUser(role: UserRole.user, isActive: false);

        await repository.create(admin);
        await repository.create(activeUser);
        await repository.create(inactiveUser);

        // Act
        final activeUsersOnly = await repository.getFiltered({'isActive': true});
        final adminUsersOnly = await repository.getFiltered({'role': 'admin'});
        final activeRegularUsers = await repository.getFiltered({
          'role': 'user',
          'isActive': true,
        });

        // Assert
        expect(activeUsersOnly.length, equals(2));
        expect(adminUsersOnly.length, equals(1));
        expect(activeRegularUsers.length, equals(1));
      });
    });

    group('Batch Operations', () {
      test('should create multiple users', () async {
        // Arrange
        final users = TestUtils.createTestUsers(3);

        // Act
        final result = await repository.createBatch(users);

        // Assert
        expect(result.length, equals(3));
        expect(await repository.count(), equals(3));
      });

      test('should update multiple users', () async {
        // Arrange
        final users = TestUtils.createTestUsers(3);
        await repository.createBatch(users);

        final updatedUsers = users.map((user) => user.copyWith(
          firstName: 'Updated',
        )).toList();

        // Act
        final result = await repository.updateBatch(updatedUsers);

        // Assert
        expect(result.length, equals(3));
        
        for (final user in updatedUsers) {
          final retrieved = await repository.getById(user.id);
          expect(retrieved!.firstName, equals('Updated'));
        }
      });

      test('should delete multiple users', () async {
        // Arrange
        final users = TestUtils.createTestUsers(3);
        await repository.createBatch(users);

        final userIds = users.map((user) => user.id).toList();

        // Act
        await repository.deleteBatch(userIds);

        // Assert
        expect(await repository.count(), equals(0));
        
        for (final id in userIds) {
          expect(await repository.exists(id), isFalse);
        }
      });
    });

    group('User Activity Management', () {
      test('should handle user activities', () async {
        // Arrange
        final user = TestUtils.createTestUser();
        await repository.create(user);

        final activity = TestUtils.createTestUserActivity(userId: user.id);

        // Act
        await repository.addUserActivity(user.id, activity);
        final activities = await repository.getUserActivity(user.id);

        // Assert
        expect(activities.length, equals(1));
        expect(activities.first.userId, equals(user.id));
      });

      test('should get user activities with pagination', () async {
        // Arrange
        final user = TestUtils.createTestUser();
        await repository.create(user);

        // Add multiple activities
        for (int i = 0; i < 15; i++) {
          final activity = TestUtils.createTestUserActivity(
            userId: user.id,
            action: 'action_$i',
          );
          await repository.addUserActivity(user.id, activity);
        }

        // Act
        final page1 = await repository.getUserActivity(user.id, page: 1, pageSize: 10);
        final page2 = await repository.getUserActivity(user.id, page: 2, pageSize: 10);

        // Assert
        expect(page1.length, equals(10));
        expect(page2.length, equals(5));
      });
    });
  });
}

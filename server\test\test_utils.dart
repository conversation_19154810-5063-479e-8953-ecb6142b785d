import 'package:shared/shared.dart';

/// Test utilities for creating mock data and test helpers
class TestUtils {
  /// Generate test user data
  static User createTestUser({
    String? id,
    String? username,
    String? email,
    String? firstName,
    String? lastName,
    UserRole? role,
    int? xp,
    int? level,
    bool? isActive,
  }) {
    return User(
      id: id ?? IdGenerator.generateUserId(),
      username: username ?? 'testuser_${IdGenerator.generateShortId()}',
      email: email ?? 'test_${IdGenerator.generateShortId()}@example.com',
      firstName: firstName ?? 'Test',
      lastName: lastName ?? 'User',
      role: role ?? UserRole.user,
      xp: xp ?? 0,
      level: level ?? 1,
      createdAt: DateTime.now(),
      isActive: isActive ?? true,
    );
  }

  /// Generate test quest data
  static Quest createTestQuest({
    String? id,
    String? title,
    String? description,
    String? createdBy,
    QuestDifficulty? difficulty,
    QuestCategory? category,
    QuestStatus? status,
    int? xpReward,
    DateTime? deadline,
  }) {
    return Quest(
      id: id ?? IdGenerator.generateQuestId(),
      title: title ?? 'Test Quest ${IdGenerator.generateShortId()}',
      description: description ?? 'A test quest for unit testing',
      createdBy: createdBy ?? IdGenerator.generateUserId(),
      difficulty: difficulty ?? QuestDifficulty.easy,
      category: category ?? QuestCategory.personal,
      status: status ?? QuestStatus.active,
      xpReward: xpReward ?? 100,
      createdAt: DateTime.now(),
      deadline: deadline,
    );
  }

  /// Generate test notification data
  static Notification createTestNotification({
    String? id,
    String? userId,
    String? title,
    String? message,
    NotificationType? type,
    bool? isRead,
  }) {
    return Notification(
      id: id ?? IdGenerator.generateNotificationId(),
      userId: userId ?? IdGenerator.generateUserId(),
      title: title ?? 'Test Notification',
      message: message ?? 'This is a test notification',
      type: type ?? NotificationType.info,
      isRead: isRead ?? false,
      createdAt: DateTime.now(),
    );
  }

  /// Generate test achievement data
  static Achievement createTestAchievement({
    String? id,
    String? title,
    String? description,
    String? category,
    int? xpReward,
    AchievementType? type,
  }) {
    return Achievement(
      id: id ?? IdGenerator.generateAchievementId(),
      title: title ?? 'Test Achievement',
      description: description ?? 'A test achievement for unit testing',
      category: category ?? 'test',
      xpReward: xpReward ?? 50,
      type: type ?? AchievementType.milestone,
      createdAt: DateTime.now(),
    );
  }

  /// Generate test leaderboard entry data
  static LeaderboardEntry createTestLeaderboardEntry({
    String? userId,
    String? username,
    int? score,
    int? rank,
  }) {
    return LeaderboardEntry(
      userId: userId ?? IdGenerator.generateUserId(),
      username: username ?? 'testuser_${IdGenerator.generateShortId()}',
      score: score ?? 1000,
      rank: rank ?? 1,
      lastUpdated: DateTime.now(),
    );
  }

  /// Generate test user activity data
  static UserActivity createTestUserActivity({
    String? id,
    String? userId,
    String? action,
    String? description,
    Map<String, dynamic>? metadata,
  }) {
    return UserActivity(
      id: id ?? IdGenerator.generateShortId(),
      userId: userId ?? IdGenerator.generateUserId(),
      action: action ?? 'test_action',
      description: description ?? 'Test activity description',
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    );
  }

  /// Generate test dashboard stats
  static DashboardStats createTestDashboardStats({
    int? totalUsers,
    int? activeUsers,
    int? totalQuests,
    int? completedQuests,
    int? totalNotifications,
    int? unreadNotifications,
  }) {
    return DashboardStats(
      totalUsers: totalUsers ?? 100,
      activeUsers: activeUsers ?? 75,
      totalQuests: totalQuests ?? 50,
      completedQuests: completedQuests ?? 30,
      totalNotifications: totalNotifications ?? 200,
      unreadNotifications: unreadNotifications ?? 25,
      generatedAt: DateTime.now(),
    );
  }

  /// Generate test system health data
  static SystemHealth createTestSystemHealth({
    bool? isHealthy,
    double? cpuUsage,
    double? memoryUsage,
    int? activeConnections,
  }) {
    return SystemHealth(
      isHealthy: isHealthy ?? true,
      cpuUsage: cpuUsage ?? 45.5,
      memoryUsage: memoryUsage ?? 60.2,
      activeConnections: activeConnections ?? 25,
      uptime: const Duration(hours: 24),
      timestamp: DateTime.now(),
    );
  }

  /// Generate test WebSocket message
  static WebSocketMessage createTestWebSocketMessage({
    String? id,
    WebSocketMessageType? type,
    Map<String, dynamic>? data,
    String? userId,
    String? sessionId,
  }) {
    return WebSocketMessage(
      id: id ?? IdGenerator.generateShortId(),
      type: type ?? WebSocketMessageType.notification,
      data: data ?? {'test': 'data'},
      timestamp: DateTime.now(),
      userId: userId,
      sessionId: sessionId,
    );
  }

  /// Generate test API response
  static ApiResponse<T> createTestApiResponse<T>({
    required T data,
    bool? success,
    String? message,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: success ?? true,
      data: data,
      message: message ?? 'Test operation successful',
      statusCode: statusCode ?? 200,
      timestamp: DateTime.now(),
    );
  }

  /// Generate test error API response
  static ApiResponse<T> createTestErrorResponse<T>({
    String? message,
    Map<String, dynamic>? errors,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message ?? 'Test error occurred',
      errors: errors ?? {'test': 'error'},
      statusCode: statusCode ?? 400,
      timestamp: DateTime.now(),
    );
  }

  /// Generate test validation exception
  static ValidationException createTestValidationException({
    String? message,
    Map<String, String>? fieldErrors,
    String? code,
  }) {
    return ValidationException(
      message: message ?? 'Test validation failed',
      fieldErrors: fieldErrors ?? {'testField': 'Test field error'},
      code: code ?? 'TEST_VALIDATION_ERROR',
    );
  }

  /// Generate test auth exception
  static AuthException createTestAuthException({
    String? message,
    String? code,
  }) {
    return AuthException(
      message: message ?? 'Test authentication failed',
      code: code ?? 'TEST_AUTH_ERROR',
    );
  }

  /// Generate test quest exception
  static QuestException createTestQuestException({
    String? message,
    String? code,
    String? questId,
    String? userId,
  }) {
    return QuestException(
      message: message ?? 'Test quest error',
      code: code ?? 'TEST_QUEST_ERROR',
      questId: questId,
      userId: userId,
    );
  }

  /// Generate test API exception
  static ApiException createTestApiException({
    String? message,
    int? statusCode,
    String? code,
    Map<String, dynamic>? errors,
  }) {
    return ApiException(
      message: message ?? 'Test API error',
      statusCode: statusCode ?? 500,
      code: code ?? 'TEST_API_ERROR',
      errors: errors,
    );
  }

  /// Generate multiple test users
  static List<User> createTestUsers(int count) {
    return List.generate(count, (index) => createTestUser(
      username: 'testuser_$index',
      email: 'test$<EMAIL>',
    ));
  }

  /// Generate multiple test quests
  static List<Quest> createTestQuests(int count, {String? createdBy}) {
    return List.generate(count, (index) => createTestQuest(
      title: 'Test Quest $index',
      createdBy: createdBy ?? IdGenerator.generateUserId(),
    ));
  }

  /// Generate multiple test notifications
  static List<Notification> createTestNotifications(int count, {String? userId}) {
    return List.generate(count, (index) => createTestNotification(
      title: 'Test Notification $index',
      userId: userId ?? IdGenerator.generateUserId(),
    ));
  }

  /// Generate multiple test achievements
  static List<Achievement> createTestAchievements(int count) {
    return List.generate(count, (index) => createTestAchievement(
      title: 'Test Achievement $index',
    ));
  }

  /// Generate test leaderboard
  static List<LeaderboardEntry> createTestLeaderboard(int count) {
    return List.generate(count, (index) => createTestLeaderboardEntry(
      username: 'user_$index',
      score: 1000 - (index * 100),
      rank: index + 1,
    ));
  }

  /// Wait for async operations in tests
  static Future<void> waitForAsync([Duration? duration]) async {
    await Future.delayed(duration ?? const Duration(milliseconds: 10));
  }

  /// Create test date in the past
  static DateTime pastDate({int? daysAgo}) {
    return DateTime.now().subtract(Duration(days: daysAgo ?? 1));
  }

  /// Create test date in the future
  static DateTime futureDate({int? daysFromNow}) {
    return DateTime.now().add(Duration(days: daysFromNow ?? 1));
  }

  /// Generate random test string
  static String randomString([int? length]) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return String.fromCharCodes(Iterable.generate(
      length ?? 8,
      (_) => chars.codeUnitAt(random % chars.length),
    ));
  }

  /// Generate random test email
  static String randomEmail() {
    return '${randomString(8)}@test.com';
  }

  /// Generate random test username
  static String randomUsername() {
    return 'user_${randomString(6)}';
  }
}

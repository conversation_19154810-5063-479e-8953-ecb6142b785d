/// API-related exception classes
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? code;
  final Map<String, dynamic>? errors;
  final dynamic originalError;
  final StackTrace? stackTrace;

  const ApiException({
    required this.message,
    this.statusCode,
    this.code,
    this.errors,
    this.originalError,
    this.stackTrace,
  });

  @override
  String toString() {
    final buffer = StringBuffer('ApiException: $message');
    if (statusCode != null) buffer.write(' (Status: $statusCode)');
    if (code != null) buffer.write(' (Code: $code)');
    return buffer.toString();
  }

  /// Create from HTTP response
  factory ApiException.fromResponse({
    required int statusCode,
    required String message,
    String? code,
    Map<String, dynamic>? errors,
  }) {
    return ApiException(
      message: message,
      statusCode: statusCode,
      code: code,
      errors: errors,
    );
  }

  /// Network error
  factory ApiException.network({
    String message = 'Network error occurred',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return ApiException(
      message: message,
      code: 'NETWORK_ERROR',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Timeout error
  factory ApiException.timeout({
    String message = 'Request timed out',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return ApiException(
      message: message,
      code: 'TIMEOUT_ERROR',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Server error
  factory ApiException.server({
    String message = 'Server error occurred',
    int? statusCode,
    String? code,
    Map<String, dynamic>? errors,
  }) {
    return ApiException(
      message: message,
      statusCode: statusCode ?? 500,
      code: code ?? 'SERVER_ERROR',
      errors: errors,
    );
  }

  /// Client error
  factory ApiException.client({
    String message = 'Client error occurred',
    int? statusCode,
    String? code,
    Map<String, dynamic>? errors,
  }) {
    return ApiException(
      message: message,
      statusCode: statusCode ?? 400,
      code: code ?? 'CLIENT_ERROR',
      errors: errors,
    );
  }

  /// Parse error
  factory ApiException.parse({
    String message = 'Failed to parse response',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return ApiException(
      message: message,
      code: 'PARSE_ERROR',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Unknown error
  factory ApiException.unknown({
    String message = 'Unknown error occurred',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return ApiException(
      message: message,
      code: 'UNKNOWN_ERROR',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Check if this is a network error
  bool get isNetworkError => code == 'NETWORK_ERROR';

  /// Check if this is a timeout error
  bool get isTimeoutError => code == 'TIMEOUT_ERROR';

  /// Check if this is a server error (5xx)
  bool get isServerError => statusCode != null && statusCode! >= 500;

  /// Check if this is a client error (4xx)
  bool get isClientError => statusCode != null && statusCode! >= 400 && statusCode! < 500;

  /// Check if this is an authentication error
  bool get isAuthError => statusCode == 401 || code == 'AUTH_ERROR';

  /// Check if this is an authorization error
  bool get isAuthorizationError => statusCode == 403 || code == 'AUTHORIZATION_ERROR';

  /// Check if this is a not found error
  bool get isNotFoundError => statusCode == 404 || code == 'NOT_FOUND';

  /// Check if this is a validation error
  bool get isValidationError => statusCode == 422 || code == 'VALIDATION_ERROR';

  /// Check if this is a rate limit error
  bool get isRateLimitError => statusCode == 429 || code == 'RATE_LIMIT_ERROR';
}

/// HTTP-specific exception
class HttpException extends ApiException {
  final String method;
  final String url;
  final Map<String, String>? headers;
  final dynamic requestBody;

  const HttpException({
    required String message,
    required this.method,
    required this.url,
    int? statusCode,
    String? code,
    Map<String, dynamic>? errors,
    this.headers,
    this.requestBody,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          statusCode: statusCode,
          code: code,
          errors: errors,
          originalError: originalError,
          stackTrace: stackTrace,
        );

  @override
  String toString() {
    return 'HttpException: $method $url - $message (Status: $statusCode)';
  }
}

/// WebSocket-specific exception
class WebSocketException extends ApiException {
  final String? url;
  final int? closeCode;
  final String? closeReason;

  const WebSocketException({
    required String message,
    this.url,
    this.closeCode,
    this.closeReason,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          code: code,
          originalError: originalError,
          stackTrace: stackTrace,
        );

  @override
  String toString() {
    final buffer = StringBuffer('WebSocketException: $message');
    if (url != null) buffer.write(' (URL: $url)');
    if (closeCode != null) buffer.write(' (Close Code: $closeCode)');
    if (closeReason != null) buffer.write(' (Reason: $closeReason)');
    return buffer.toString();
  }

  /// Connection failed
  factory WebSocketException.connectionFailed({
    String? url,
    String message = 'WebSocket connection failed',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return WebSocketException(
      message: message,
      url: url,
      code: 'CONNECTION_FAILED',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Connection closed unexpectedly
  factory WebSocketException.connectionClosed({
    String? url,
    int? closeCode,
    String? closeReason,
    String message = 'WebSocket connection closed unexpectedly',
  }) {
    return WebSocketException(
      message: message,
      url: url,
      closeCode: closeCode,
      closeReason: closeReason,
      code: 'CONNECTION_CLOSED',
    );
  }

  /// Message send failed
  factory WebSocketException.sendFailed({
    String? url,
    String message = 'Failed to send WebSocket message',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return WebSocketException(
      message: message,
      url: url,
      code: 'SEND_FAILED',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }
}

/// Cache-specific exception
class CacheException extends ApiException {
  final String? key;
  final String operation;

  const CacheException({
    required String message,
    required this.operation,
    this.key,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          code: code,
          originalError: originalError,
          stackTrace: stackTrace,
        );

  @override
  String toString() {
    final buffer = StringBuffer('CacheException: $operation - $message');
    if (key != null) buffer.write(' (Key: $key)');
    return buffer.toString();
  }

  /// Cache miss
  factory CacheException.miss({
    required String key,
    String message = 'Cache miss',
  }) {
    return CacheException(
      message: message,
      operation: 'get',
      key: key,
      code: 'CACHE_MISS',
    );
  }

  /// Cache write failed
  factory CacheException.writeFailed({
    required String key,
    String message = 'Failed to write to cache',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return CacheException(
      message: message,
      operation: 'set',
      key: key,
      code: 'WRITE_FAILED',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Cache read failed
  factory CacheException.readFailed({
    required String key,
    String message = 'Failed to read from cache',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return CacheException(
      message: message,
      operation: 'get',
      key: key,
      code: 'READ_FAILED',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }
}
